<!doctype html>
<html lang="en">

<head>
  {{> partials/head title="Masonry - Documentation"}}
</head>

<body class="bg-light">


  <!-- navbar -->
  {{> navbar/navbar
  logo="/assets/images/logo/logo-dark.svg"
  class="navbar-sticky navbar-light bg-light border-bottom"
  active="components"
  button="true"
  }}


  <div class="offcanvas-wrap">
    <section class="split">
      <div class="container">
        <div class="row justify-content-between">



          <div class="col-lg-3 split-sidebar">
            <!-- table of contents-->
            {{> components/toc
            section="components"
            active="isotope"
            }}
          </div>


          <!-- content -->
          <div class="col-lg-9 split-content">


            <!-- text -->
            <section class="pb-5 border-bottom">
              <h1 class="mb-1">Isotope</h1>
              <p class="lead text-secondary">Filter & sort magical layouts.
              </p>
              <a href="https://isotope.metafizzy.co" class="underline action">Isotope Documentation <i
                  class="bi bi-arrow-up-right"></i></a>
            </section>



            <!-- layout -->
            <section>
              <h3 class="fs-4">Layout</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white">
                  <div class="row g-3 gallery-1" data-isotope>
                    <div class="col-md-6">
                      {{> components/image
                      link="/assets/images/small-1.jpg"
                      lightbox="true"
                      gallery="gallery-1"
                      class="media-image equal-2-1 equal-md-1-1"
                      }}
                    </div>
                    <div class="col-md-6">
                      {{> components/image
                      link="/assets/images/small-2.jpg"
                      lightbox="true"
                      gallery="gallery-1"
                      class="media-image equal-2-1 equal-md-3-4"
                      }}
                    </div>
                    <div class="col-md-6">
                      {{> components/image
                      link="/assets/images/small-3.jpg"
                      lightbox="true"
                      gallery="gallery-1"
                      class="media-image equal-2-1 equal-md-3-4"
                      }}
                    </div>
                    <div class="col-md-6">
                      {{> components/image
                      link="/assets/images/small-4.jpg"
                      lightbox="true"
                      gallery="gallery-1"
                      class="media-image equal-2-1 equal-md-1-1"
                      }}
                    </div>
                  </div>
                </div>
                <div class="card-footer">
                  <script type="text/plain" class="language-html">
                    <div class="row g-3 gallery-1" data-isotope>
                      <div class="col-md-6">
                        {{> components/image
                        link="/assets/images/small-1.jpg"
                        lightbox="true"
                        gallery="gallery-1"
                        class="media-image equal-2-1 equal-md-1-1"
                        }}
                      </div>
                      <div class="col-md-6">
                        {{> components/image
                        link="/assets/images/small-2.jpg"
                        lightbox="true"
                        gallery="gallery-1"
                        class="media-image equal-2-1 equal-md-3-4"
                        }}
                      </div>
                      <div class="col-md-6">
                        {{> components/image
                        link="/assets/images/small-3.jpg"
                        lightbox="true"
                        gallery="gallery-1"
                        class="media-image equal-2-1 equal-md-3-4"
                        }}
                      </div>
                      <div class="col-md-6">
                        {{> components/image
                        link="/assets/images/small-4.jpg"
                        lightbox="true"
                        gallery="gallery-1"
                        class="media-image equal-2-1 equal-md-1-1"
                        }}
                      </div>
                    </div>
                  </script>
                </div>
              </div>
            </section>


            <!-- filtration -->
            <section>
              <h3 class="fs-4">Filtration</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white">
                  <div class="mb-3">
                    <button class="btn btn-filter rounded-pill current" data-filter="*"
                      data-target="#filter1">all</button>
                    <button class="btn btn-filter rounded-pill" data-filter=".filter-blue"
                      data-target="#filter1">blue</button>
                    <button class="btn btn-filter rounded-pill" data-filter=".filter-green"
                      data-target="#filter1">green</button>
                  </div>
                  <div class="row mb-5 g-3" id="filter1" data-isotope>
                    <div class="col-md-6 filter-blue">
                      <div class="equal-1-1 bg-blue"></div>
                    </div>
                    <div class="col-md-6 filter-green">
                      <div class="equal-2-1 bg-green"></div>
                    </div>
                    <div class="col-md-6 filter-blue">
                      <div class="equal-1-1 bg-blue"></div>
                    </div>
                    <div class="col-md-6 filter-green">
                      <div class="equal-2-1 bg-green"></div>
                    </div>
                  </div>
                </div>
                <div class="card-footer">
                  <script type="text/plain" class="language-html">
                    <div class="mb-3">
                      <button class="btn btn-filter rounded-pill current" data-filter="*"
                        data-target="#filter1">all</button>
                      <button class="btn btn-filter rounded-pill" data-filter=".filter-blue"
                        data-target="#filter1">blue</button>
                      <button class="btn btn-filter rounded-pill" data-filter=".filter-green"
                        data-target="#filter1">green</button>
                    </div>
                    <div class="row mb-5 g-3" id="filter1" data-isotope>
                      <div class="col-md-6 filter-blue">
                        <div class="equal-1-1 bg-blue"></div>
                      </div>
                      <div class="col-md-6 filter-green">
                        <div class="equal-2-1 bg-green"></div>
                      </div>
                      <div class="col-md-6 filter-blue">
                        <div class="equal-1-1 bg-blue"></div>
                      </div>
                      <div class="col-md-6 filter-green">
                        <div class="equal-2-1 bg-green"></div>
                      </div>
                    </div>
                  </script>
                </div>
              </div>
            </section>




          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- table of contents - mobile-->
  {{> components/toc-mobile }}


  <!-- javascript -->
  {{> partials/scripts}}
</body>

</html>