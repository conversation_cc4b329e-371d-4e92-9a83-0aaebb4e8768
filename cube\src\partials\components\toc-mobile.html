<div class="position-fixed bottom-0 end-0 p-2 level-1">
  <button class="btn btn-white btn-icon d-lg-none rounded-pill shadow" type="button" data-bs-toggle="offcanvas"
    data-bs-target="#offcanvasExample" aria-controls="offcanvasExample">
    <i class="bi bi-justify-right fs-4"></i>
  </button>
</div>


<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasExample" aria-labelledby="offcanvasExampleLabel">
  <div class="offcanvas-header">
    <h5 class="offcanvas-title" id="offcanvasExampleLabel">Table of Contents</h5>
    <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body">
    <ul class="nav nav-minimal flex-column" id="toc-mob-nav">
      <li class="nav-item">
        <a class="nav-link fs-lg" href="#menu-mob-1" data-bs-toggle="collapse" role="button" aria-expanded="false"
          aria-controls="menu-mob-1">Get Started</a>
        <div class="collapse" id="menu-mob-1" data-bs-parent="#toc-mob-nav">
          <ul class="nav nav-minimal flex-column">
            <li class="nav-item">
              <a class="nav-link {{#equals active 'introduction'}}active{{/equals}}" href="index.html">Introduction</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'changelog'}}active{{/equals}}" href="changelog.html">Changelog</a>
            </li>
          </ul>
        </div>
      </li>
      <li class="nav-item">
        <a class="nav-link fs-lg" href="#menu-mob-2" data-bs-toggle="collapse" role="button" aria-expanded="false"
          aria-controls="menu-mob-2">Components</a>
        <div class="collapse" id="menu-mob-2" data-bs-parent="#toc-mob-nav">
          <ul class="nav nav-minimal flex-column">
            <li class="nav-item">
              <a class="nav-link {{#equals active 'accordion'}}active{{/equals}}" href="accordion.html">Accordion</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'alerts'}}active{{/equals}}" href="alerts.html">Alerts</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'avatars'}}active{{/equals}}" href="avatars.html">Avatars</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'badge'}}active{{/equals}}" href="badge.html">Badge</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'breadcrumb'}}active{{/equals}}" href="breadcrumb.html">Breadcrumb</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'buttons'}}active{{/equals}}" href="buttons.html">Buttons</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'card'}}active{{/equals}}" href="card.html">Card</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'carousel'}}active{{/equals}}" href="carousel.html">Carousel</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'countdown'}}active{{/equals}}" href="countdown.html">Countdown</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'dropdowns'}}active{{/equals}}" href="dropdowns.html">Dropdowns</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'forms'}}active{{/equals}}" href="forms.html">Forms</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'frames'}}active{{/equals}}" href="frames.html">Frames</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'icons'}}active{{/equals}}" href="icons.html">Icons</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'isotope'}}active{{/equals}}" href="isotope.html">Isotope</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'lightbox'}}active{{/equals}}" href="lightbox.html">Lightbox</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'lists'}}active{{/equals}}" href="lists.html">Lists</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'modal'}}active{{/equals}}" href="modal.html">Modal</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'navbar'}}active{{/equals}}" href="navbar.html">Navbar</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'smoothscroll'}}active{{/equals}}"
                href="smoothscroll.html">Smoothscroll</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'spinners'}}active{{/equals}}" href="spinners.html">Spinners</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'steps'}}active{{/equals}}" href="steps.html">Steps</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'typed'}}active{{/equals}}" href="typed.html">Typed</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'video'}}active{{/equals}}" href="video.html">Video</a>
            </li>
          </ul>
        </div>
      </li>
      <li class="nav-item">
        <a class="nav-link fs-lg" href="#menu-mob-3" data-bs-toggle="collapse" role="button" aria-expanded="false"
          aria-controls="menu-mob-3">Utilities</a>
        <div class="collapse" id="menu-mob-3" data-bs-parent="#toc-mob-nav">
          <ul class="nav nav-minimal flex-column">
            <li class="nav-item">
              <a class="nav-link {{#equals active 'aos'}}active{{/equals}}" href="aos.html">Animate on Scroll</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'sizing'}}active{{/equals}}" href="sizing.html">Sizing</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'background'}}active{{/equals}}" href="background.html">Background</a>
            </li>
            <li class="nav-item">
              <a class="nav-link {{#equals active 'shadows'}}active{{/equals}}" href="shadows.html">Shadows</a>
            </li>
          </ul>
        </div>
      </li>
    </ul>
  </div>
</div>