import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Property from './models/Property.js';

// Load environment variables
dotenv.config();

async function checkPropertyData() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/makeithome');
    console.log('✅ Connected to MongoDB\n');

    // Get all properties
    console.log('=== ALL PROPERTIES ===');
    const allProperties = await Property.find().sort({ createdAt: -1 });
    console.log(`Total properties in database: ${allProperties.length}\n`);

    if (allProperties.length === 0) {
      console.log('❌ No properties found in database!');
      console.log('This might explain why the filter isn\'t working - there\'s no data to filter.\n');
    } else {
      // Show summary by type and status
      console.log('=== PROPERTY BREAKDOWN ===');
      const breakdown = {};
      
      allProperties.forEach(prop => {
        const key = `${prop.type || 'unknown'}-${prop.status || 'unknown'}`;
        breakdown[key] = (breakdown[key] || 0) + 1;
      });

      Object.entries(breakdown).forEach(([key, count]) => {
        console.log(`${key}: ${count} properties`);
      });
      console.log('');

      // Show sale properties specifically (what the Properties.vue page loads)
      console.log('=== SALE PROPERTIES (what Properties.vue loads) ===');
      const saleProperties = allProperties.filter(p => p.type === 'sale' && p.status !== 'upcoming');
      console.log(`Sale properties (excluding upcoming): ${saleProperties.length}`);
      
      if (saleProperties.length > 0) {
        console.log('\nSale Properties Details:');
        saleProperties.forEach((prop, index) => {
          console.log(`${index + 1}. "${prop.title}" - Status: ${prop.status} - Price: $${prop.price?.toLocaleString() || 'N/A'}`);
        });
        
        // Check status distribution for sale properties
        console.log('\nSale Properties Status Distribution:');
        const statusCounts = {};
        saleProperties.forEach(prop => {
          statusCounts[prop.status] = (statusCounts[prop.status] || 0) + 1;
        });
        
        Object.entries(statusCounts).forEach(([status, count]) => {
          console.log(`  ${status}: ${count} properties`);
        });
      } else {
        console.log('❌ No sale properties found! This explains the filtering issue.');
      }
    }

    console.log('\n=== FILTER TEST SIMULATION ===');
    const saleProps = allProperties.filter(p => p.type === 'sale' && p.status !== 'upcoming');
    
    // Test available filter
    const availableProps = saleProps.filter(p => p.status === 'available');
    console.log(`Available Only filter would show: ${availableProps.length} properties`);
    
    // Test sold filter  
    const soldProps = saleProps.filter(p => p.status === 'sold');
    console.log(`Sold Only filter would show: ${soldProps.length} properties`);
    
    // Test show all filter
    console.log(`Show All filter would show: ${saleProps.length} properties`);

  } catch (error) {
    console.error('❌ Error checking property data:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 MongoDB connection refused. Make sure MongoDB is running:');
      console.log('   - On Windows: Start MongoDB service');
      console.log('   - On Mac: brew services start mongodb-community');
      console.log('   - On Linux: sudo systemctl start mongod');
    }
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

checkPropertyData();
