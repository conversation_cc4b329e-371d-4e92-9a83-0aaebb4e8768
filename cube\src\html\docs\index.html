<!doctype html>
<html lang="en">

<head>
  {{> partials/head title="Introduction - Documentation"}}
</head>

<body class="bg-light">


  <!-- navbar -->
  {{> navbar/navbar
  logo="/assets/images/logo/logo-dark.svg"
  class="navbar-sticky navbar-light bg-light border-bottom"
  active="introduction"
  button="true"
  }}


  <div class="offcanvas-wrap">
    <section class="split">
      <div class="container">
        <div class="row justify-content-between">



          <div class="col-lg-3 split-sidebar">
            <!-- table of contents-->
            {{> components/toc
            section="get-started"
            active="introduction"
            }}
          </div>


          <!-- content -->
          <div class="col-lg-9 split-content">


            <!-- text -->
            <section class="pb-5">
              <h1 class="mb-1">Introduction</h1>
              <p class="lead text-secondary">This is a quick quide that will help you build your first page.</p>
            </section>


            <div class="alert alert-primary" role="alert">
              <h4 class="fs-6">You can use CUBE as is out of the box.</h4>
              <p> The webflow workflow can be bypassed all together if you prefer to simply edit the static HTML and CSS
                files. All the ready to use files are locate in <code class="bg-primary text-white">dist</code> folder.
                But if you are experienced developer and want to speed up development process follow steps bellow.</p>
            </div>


            <!-- setup guide -->
            <section>
              <h3 class="fs-4">Dev setup</h3>
              <ol class="list-group list-group-minimal list-group-numbered">
                <li class="list-group-item">
                  <p>Download and install Node.js and Gulp to get started.
                    To install Node visit <a href="https://nodejs.org/en/" class="underline">https://nodejs.org/en/</a>
                    To install Gulp, run the following command npm install gulp -g</p>
                </li>
                <li class="list-group-item">
                  <p>Open terminal, make sure your command line prompt is at the root of the theme folder.</p>
                </li>
                <li class="list-group-item">
                  <p>Run <code>npm install</code> to install all theme's dependencies.</p>
                </li>
              </ol>
            </section>

            <!-- setup guide -->
            <section>
              <h3 class="fs-4">Webpack</h3>
              <p>Webpack is used to manage theme development process. Open terminal, make sure
                your command line prompt
                is at the root of the theme folder to use following commands:</p>
              <ul class="list-unstyled">
                <li>
                  <p class="text-secondary"><code>npm start</code> This will compile and track all the SCSS, JS and HTML
                    files for changes,
                    start a local webserver.</p>
                </li>
                <li class="mt-1">
                  <p class="text-secondary"><code>npm run build</code> Generates a /dist directory with all the
                    production files.</p>
                </li>
              </ul>
            </section>


            <!-- file structure -->
            <section>
              <h3 class="fs-4">File Structure</h3>

              <div class="card bg-white">
                <div class="card-body">
                  <ul class="file-structure">
                    <li>
                      <a data-bs-toggle="collapse" href="#folder-cube" role="button" aria-expanded="true"
                        aria-controls="folder-cube">
                        CUBE</a>
                      <ul class="collapse show" id="folder-cube">
                        <li>
                          <span class="folder">dist <i class="bi bi-info-circle ms-1 text-muted"
                              data-bs-toggle="tooltip" data-bs-placement="top" title="Ready To Use Files"></i></span>
                        </li>
                        <li>
                          <span class="folder">node_modules <i class="bi bi-info-circle ms-1 text-muted"
                              data-bs-toggle="tooltip" data-bs-placement="top"
                              title="NPM installs theme dependencies here"></i></span>
                        </li>
                        <li>
                          <a data-bs-toggle="collapse" href="#folder-src" role="button" aria-expanded="false"
                            aria-controls="folder-src">
                            src<i class="bi bi-info-circle ms-1 text-muted" data-bs-toggle="tooltip"
                              data-bs-placement="top" title="Development Files"></i></a>
                          <ul class="collapse" id="folder-src">
                            <li><span class="folder">html</span></li>
                            <li><span class="folder">images</span></li>
                            <li><span class="folder">js</span></li>
                            <li><span class="folder">partials</span></li>
                            <li><span class="folder">scss</span></li>
                            <li><span class="folder">video</span></li>
                          </ul>
                        </li>
                        <li><span>README.md</span></li>
                        <li><span>webpack.config.js</span></li>
                        <li><span>package-lock.js</span></li>
                        <li><span>package.js</span></li>
                      </ul>
                    </li>
                  </ul>
                </div>
              </div>
            </section>

          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- table of contents - mobile-->
  {{> components/toc-mobile }}


  <!-- javascript -->
  {{> partials/scripts}}
</body>

</html>