<!DOCTYPE html>
<html>
<head>
    <title>Test Cloudinary Upload</title>
</head>
<body>
    <h1>Test Cloudinary Upload</h1>
    <form id="uploadForm" enctype="multipart/form-data">
        <input type="file" name="testImage" accept="image/*" required>
        <button type="submit">Upload Test Image</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = document.querySelector('input[type="file"]');
            formData.append('testImage', fileInput.files[0]);
            
            try {
                const response = await fetch('http://localhost:5000/api/admin/test-upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = '<p style="color: red;">Error: ' + error.message + '</p>';
            }
        });
    </script>
</body>
</html>
