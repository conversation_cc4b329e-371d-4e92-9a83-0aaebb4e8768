{"name": "cube", "version": "2.0.0", "scripts": {"start": "npx webpack server --open", "build": "del-cli dist && npx webpack --mode production"}, "dependencies": {"@popperjs/core": "^2.9.2", "aos": "^2.3.4", "bigpicture": "^2.5.3", "bootstrap": "^5.0.2", "bootstrap-icons": "^1.5.0", "headroom.js": "^0.12.0", "imagesloaded": "^4.1.4", "isotope-layout": "^3.0.6", "masonry-layout": "^4.2.2", "muuri": "^0.9.4", "nouislider": "^15.2.0", "plyr": "^3.6.8", "prismjs": "^1.23.0", "smooth-scroll": "^16.1.3", "tiny-slider": "^2.9.3", "typed.js": "^2.0.12"}, "private": true, "devDependencies": {"autoprefixer": "^10.2.6", "browser-sync-webpack-plugin": "^2.3.0", "copy-webpack-plugin": "^9.0.0", "css-loader": "^5.2.4", "del-cli": "^3.0.1", "file-loader": "^6.2.0", "handlebars-webpack-plugin": "^2.2.1", "mini-css-extract-plugin": "^1.6.0", "node-sass": "^6.0.0", "npm": "^7.16.0", "postcss-loader": "^5.2.0", "sass-loader": "^12.0.0", "webpack": "^5.38.1", "webpack-cli": "^4.7.2", "webpack-dev-server": "^3.11.2", "webpack-remove-empty-scripts": "^0.7.1"}}