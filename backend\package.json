{"name": "make-it-home-backend", "version": "1.0.0", "type": "module", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "manage-admins": "node scripts/manageAdmins.js"}, "dependencies": {"@azure/msal-node": "^3.8.0", "axios": "^1.12.2", "bcryptjs": "^2.4.3", "cloudinary": "^2.7.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.9.4", "sharp": "^0.34.3"}, "devDependencies": {"nodemon": "^3.0.1"}}