<template>
  <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
    <div class="container">
      <!-- Brand with Logo -->
      <router-link class="navbar-brand fw-bold text-warm-sunset d-flex align-items-center" to="/">
        <img v-if="logoLoaded" :src="logoUrl" alt="Make It Home Logo" class="logo me-2">
        <span>Make It Home LLC</span>
      </router-link>

      <!-- Mobile toggle -->
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>

      <!-- Navigation links -->
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav mx-auto">
          <li class="nav-item">
            <router-link class="nav-link text-slate-gray" to="/" @click="closeMobileMenu">Home</router-link>
          </li>
          <li class="nav-item">
            <router-link class="nav-link text-slate-gray" to="/properties" @click="closeMobileMenu">Buy</router-link>
          </li>
          <li class="nav-item">
            <router-link class="nav-link text-slate-gray" to="/rentals" @click="closeMobileMenu">Rentals</router-link>
          </li>
          <li class="nav-item">
            <router-link class="nav-link text-slate-gray" to="/upcoming" @click="closeMobileMenu">Upcoming</router-link>
          </li>
          <li class="nav-item dropdown dropdown-hover">
            <a class="nav-link dropdown-toggle text-slate-gray" href="#" id="servicesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              Services
            </a>
            <ul class="dropdown-menu" aria-labelledby="servicesDropdown">
              <li><router-link class="dropdown-item text-slate-gray" to="/sell-to-us" @click="closeMobileMenu">Sell To Us</router-link></li>
              <li><router-link class="dropdown-item text-slate-gray" to="/mortgage-calculator" @click="closeMobileMenu">Mortgage Calculator</router-link></li>
            </ul>
          </li>
          <li class="nav-item">
            <router-link class="nav-link text-slate-gray" to="/agents" @click="closeMobileMenu">Agents</router-link>
          </li>
          <li class="nav-item dropdown dropdown-hover">
            <a class="nav-link dropdown-toggle text-slate-gray" href="#" id="aboutDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              About Us
            </a>
            <ul class="dropdown-menu" aria-labelledby="aboutDropdown">
              <li><router-link class="dropdown-item text-slate-gray" to="/about-us" @click="closeMobileMenu">About Us</router-link></li>
              <li><router-link class="dropdown-item text-slate-gray" to="/about" @click="closeMobileMenu">Our Leadership</router-link></li>
              <li><router-link class="dropdown-item text-slate-gray" to="/our-vision" @click="closeMobileMenu">Our Vision</router-link></li>

              <!-- Desktop: Nested dropdown -->
              <li class="nav-item dropdown dropdown-hover dropdown-submenu d-none d-lg-block">
                <a class="dropdown-item dropdown-toggle text-slate-gray"
                   href="#"
                   id="partnersDropdown"
                   role="button"
                   data-bs-toggle="dropdown"
                   aria-expanded="false">
                  Our Partners
                </a>
                <ul class="dropdown-menu dropdown-submenu" aria-labelledby="partnersDropdown">
                  <li><router-link class="dropdown-item text-slate-gray" to="/our-partners" @click="closeMobileMenu">Our Partners</router-link></li>
                  <li><router-link class="dropdown-item text-slate-gray" to="/preferred-bidders" @click="closeMobileMenu">Preferred Bidders List</router-link></li>
                </ul>
              </li>

              <!-- Mobile: Direct links -->
              <li class="d-lg-none">
                <a class="dropdown-item text-slate-gray partners-header"
                   href="#"
                   @click="togglePartnersSubmenu($event)">
                  <i class="fas fa-chevron-down me-2 partners-arrow" :class="{ 'rotated': showPartnersSubmenu }"></i>
                  Our Partners
                </a>
              </li>
              <li class="d-lg-none partners-submenu-item" :class="{ 'show': showPartnersSubmenu }">
                <router-link class="dropdown-item text-slate-gray ps-4" to="/our-partners" @click="closeMobileMenu">
                  <i class="fas fa-building me-2"></i>Our Partners
                </router-link>
              </li>
              <li class="d-lg-none partners-submenu-item" :class="{ 'show': showPartnersSubmenu }">
                <router-link class="dropdown-item text-slate-gray ps-4" to="/preferred-bidders" @click="closeMobileMenu">
                  <i class="fas fa-list me-2"></i>Preferred Bidders List
                </router-link>
              </li>
            </ul>
          </li>
        </ul>
        <router-link to="/contact" class="btn btn-warm-sunset" @click="closeMobileMenu">Contact Us</router-link>
      </div>
    </div>
  </nav>
</template>

<script>
export default {
  name: 'Navbar',
  data() {
    return {
      logoUrl: '/businessimages/makeitHomePNG.PNG',
      logoLoaded: true,
      showPartnersSubmenu: false
    }
  },
  mounted() {
    this.loadLogo()
  },
  watch: {
    // Watch for route changes and close mobile menu
    '$route'() {
      this.closeMobileMenu()
    }
  },
  methods: {
    // Close mobile menu when route changes
    closeMobileMenu() {
      const navbarCollapse = document.getElementById('navbarNav')
      if (navbarCollapse && navbarCollapse.classList.contains('show')) {
        // Use Bootstrap's collapse method to close the menu
        const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
          toggle: false
        })
        bsCollapse.hide()
      }
      // Also close submenu when closing mobile menu
      this.showPartnersSubmenu = false
    },
    togglePartnersSubmenu(event) {
      // Only handle click on mobile devices (when navbar is collapsed)
      const navbarCollapse = document.getElementById('navbarNav')
      if (navbarCollapse && navbarCollapse.classList.contains('show')) {
        event.preventDefault()
        this.showPartnersSubmenu = !this.showPartnersSubmenu
      }
    },
    loadLogo() {
      const logoVariations = [
        '/businessimages/makeitHomePNG.PNG',
        '/businessimages/makeitHomePNG.png',
        '/businessimages/makeitHomePNG.jpg',
        '/businessimages/makeitHomePNG.jpeg'
      ]

      this.tryLoadLogo(logoVariations, 0)
    },
    tryLoadLogo(variations, index) {
      if (index >= variations.length) {
        this.logoLoaded = false
        console.warn('Logo not found in any format')
        return
      }
      
      const img = new Image()
      img.onload = () => {
        this.logoUrl = variations[index]
        this.logoLoaded = true
        console.log('Logo loaded successfully from:', variations[index])
      }
      img.onerror = () => {
        console.warn('Logo not found at:', variations[index])
        this.tryLoadLogo(variations, index + 1)
      }
      img.src = variations[index]
    }
  }
}
</script>

<style scoped>
.navbar {
  padding: 1rem 0;
  background-color: #FFFFFF !important;
}

.logo {
  height: 40px;
  width: auto;
}

.navbar-brand {
  color: #EBA472 !important;
  font-weight: bold;
}

.nav-link {
  font-weight: 500;
  margin: 0 0.5rem;
  transition: color 0.3s ease;
  color: #4A4A4A !important;
}

.nav-link:hover {
  color: #EBA472 !important;
}

.dropdown-item {
  color: #4A4A4A !important;
}

.dropdown-item:hover {
  background-color: #EDE0D4;
  color: #EBA472 !important;
}

.btn-warm-sunset {
  background-color: #EBA472;
  border-color: #EBA472;
  color: #FFFFFF;
  font-weight: 600;
  padding: 0.5rem 1.5rem;
  border-radius: 0.375rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-warm-sunset:hover {
  background-color: #A15E3B;
  border-color: #A15E3B;
  color: #FFFFFF;
  text-decoration: none;
}

.router-link-active .nav-link {
  color: #EBA472 !important;
  font-weight: 600;
}

/* Cube-style dropdown hover effects */
.dropdown-hover > .dropdown-menu {
  transition: all 0.2s ease-in-out;
  display: block;
  pointer-events: none;
  opacity: 0;
  margin: 10px 0 0 0;
  transform: translateY(-10px);
}

.dropdown-hover:hover > .dropdown-menu {
  opacity: 1;
  pointer-events: auto;
  margin: 0;
  transform: translateY(0);
}

.dropdown-hover > .dropdown-toggle::after {
  transition: transform 0.2s ease;
}

.dropdown-hover:hover > .dropdown-toggle::after {
  transform: rotate(180deg);
}

.dropdown-menu {
  border: none;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  border-radius: 8px;
  padding: 0.5rem 0;
  background: #FFFFFF;
  min-width: 200px;
}

.dropdown-item {
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  border: none;
  background: transparent;
}

.dropdown-item:hover {
  background-color: #EDE0D4;
  color: #EBA472 !important;
  transform: translateX(5px);
}

.dropdown-item:focus {
  background-color: #EDE0D4;
  color: #EBA472 !important;
}

/* Nested dropdown submenu styles */
.dropdown-submenu {
  position: relative;
}

.dropdown-submenu > .dropdown-menu {
  top: 0;
  left: 100%;
  margin-top: -6px;
  margin-left: -1px;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  opacity: 0;
  pointer-events: none;
  transform: translateX(-10px);
  transition: all 0.3s ease;
}

.dropdown-submenu:hover > .dropdown-menu {
  opacity: 1;
  pointer-events: auto;
  transform: translateX(0);
}

.dropdown-submenu > .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
  transition: transform 0.2s ease;
}

.dropdown-submenu:hover > .dropdown-toggle::after {
  transform: rotate(90deg);
}

/* Remove Bootstrap's default dropdown arrow behavior */
.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
  transition: transform 0.2s ease;
}

.dropdown-hover > .dropdown-toggle::after {
  transition: transform 0.2s ease;
}

.dropdown-hover:hover > .dropdown-toggle::after {
  transform: rotate(180deg);
}

/* Smooth hover transition for nav links */
.nav-link {
  position: relative;
  transition: all 0.3s ease;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #EBA472 0%, #A15E3B 100%);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover::before {
  width: 80%;
}

/* Mobile-specific styles */
@media (max-width: 991.98px) {
  /* Mobile partners header styling */
  .partners-header {
    font-weight: 600 !important;
    color: #6c757d !important;
    cursor: pointer !important;
    user-select: none !important;
  }

  .partners-header:hover {
    background-color: #EDE0D4 !important;
    color: #EBA472 !important;
  }

  /* Partners arrow animation */
  .partners-arrow {
    transition: transform 0.3s ease !important;
    color: #EBA472 !important;
  }

  .partners-arrow.rotated {
    transform: rotate(180deg) !important;
  }

  /* Mobile submenu items */
  .partners-submenu-item {
    display: none !important;
    opacity: 0 !important;
    transform: translateY(-10px) !important;
    transition: all 0.3s ease !important;
  }

  .partners-submenu-item.show {
    display: block !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
  }

  .partners-submenu-item .dropdown-item {
    padding: 0.5rem 1rem !important;
    font-size: 0.9rem !important;
    color: #6c757d !important;
    border-left: 3px solid #EBA472 !important;
    margin-left: 0.5rem !important;
    background: rgba(235, 164, 114, 0.05) !important;
  }

  .partners-submenu-item .dropdown-item:hover {
    background-color: #EDE0D4 !important;
    color: #EBA472 !important;
    transform: translateX(5px) !important;
  }

  .partners-submenu-item .dropdown-item i {
    color: #EBA472 !important;
    width: 16px !important;
  }

  /* Ensure mobile menu items are properly spaced */
  .navbar-nav .dropdown-menu {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    padding: 0 !important;
  }

  .navbar-nav .dropdown-item {
    color: #6c757d !important;
    padding: 0.75rem 1rem !important;
  }

  .navbar-nav .dropdown-item:hover {
    background-color: #EDE0D4 !important;
    color: #EBA472 !important;
    transform: none !important;
  }
}

/* Desktop hover behavior (preserve existing functionality) */
@media (min-width: 992px) {
  .dropdown-submenu:hover > .dropdown-menu {
    opacity: 1;
    pointer-events: auto;
    transform: translateX(0);
  }
}
</style>




