// Test script to verify the property filtering logic
const testProperties = [
  { id: 1, title: 'Available House 1', status: 'available', type: 'sale', price: 300000 },
  { id: 2, title: 'Available House 2', status: 'available', type: 'sale', price: 400000 },
  { id: 3, title: 'Sold House 1', status: 'sold', type: 'sale', price: 350000 },
  { id: 4, title: 'Sold House 2', status: 'sold', type: 'sale', price: 450000 },
  { id: 5, title: 'Pending House', status: 'pending', type: 'sale', price: 375000 },
  { id: 6, title: 'Rented House', status: 'rented', type: 'rental', price: 2000 }
];

// Simulate the filtering logic from Properties.vue
function applyBaseFilters(properties, filters) {
  return properties.filter(property => {
    // Status filter - three options: available, sold, or all
    if (filters.propertyStatus === 'available' && property.status !== 'available') {
      return false
    }
    if (filters.propertyStatus === 'sold' && property.status !== 'sold') {
      return false
    }
    // If propertyStatus is 'all', show all properties regardless of status
    
    return true
  })
}

// Test cases
console.log('=== TESTING PROPERTY FILTER LOGIC ===\n');

// Test 1: Available Only (default)
console.log('Test 1: Available Only');
const availableFilter = { propertyStatus: 'available' };
const availableResults = applyBaseFilters(testProperties, availableFilter);
console.log('Expected: 2 available properties');
console.log('Actual:', availableResults.length, 'properties');
console.log('Properties:', availableResults.map(p => `${p.title} (${p.status})`));
console.log('✓ Pass:', availableResults.length === 2 && availableResults.every(p => p.status === 'available'));
console.log('');

// Test 2: Sold Only
console.log('Test 2: Sold Only');
const soldFilter = { propertyStatus: 'sold' };
const soldResults = applyBaseFilters(testProperties, soldFilter);
console.log('Expected: 2 sold properties');
console.log('Actual:', soldResults.length, 'properties');
console.log('Properties:', soldResults.map(p => `${p.title} (${p.status})`));
console.log('✓ Pass:', soldResults.length === 2 && soldResults.every(p => p.status === 'sold'));
console.log('');

// Test 3: Show All
console.log('Test 3: Show All');
const allFilter = { propertyStatus: 'all' };
const allResults = applyBaseFilters(testProperties, allFilter);
console.log('Expected: 6 properties (all)');
console.log('Actual:', allResults.length, 'properties');
console.log('Properties:', allResults.map(p => `${p.title} (${p.status})`));
console.log('✓ Pass:', allResults.length === 6);
console.log('');

console.log('=== FILTER LOGIC TEST COMPLETE ===');
