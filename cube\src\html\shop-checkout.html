<!doctype html>
<html lang="en">

<head>
  {{> partials/head title="Checkout - Shop"}}
</head>

<body>

  <!-- navbar -->
  {{> navbar/navbar
  logo="/assets/images/logo/logo-dark.svg"
  class="navbar-sticky navbar-light border-bottom"
  active="shop-checkout"
  account="true"
  shop="true"
  button="btn-outline-light"
  }}

  <div class="offcanvas-wrap">
    <section class="py-15 py-xl-20">
      <div class="container mt-5">
        <div class="row justify-content-between">
          <div class="col-xl-7 mb-5 mb-xl-0">
            <section class="mt-4">
              <h2 class="fw-bold">Address</h2>
              <form class="row g-2">
                <div class="col-12">
                  <label for="inputCountry" class="form-label">Country</label>
                  <input type="email" class="form-control" id="inputCountry" placeholder="Country" value="Germany">
                </div>
                <div class="col-12">
                  <label for="inputAddress" class="form-label">Address</label>
                  <input type="password" class="form-control" id="inputAddress" placeholder="Address">
                </div>
                <div class="col-xl-8">
                  <label for="inputCity" class="form-label">City</label>
                  <input type="text" class="form-control" id="inputCity" placeholder="City" value="Munich">
                </div>
                <div class="col-xl-4">
                  <label for="inputZip" class="form-label">Zip Code</label>
                  <input type="text" class="form-control" id="inputZip" placeholder="Zip Code">
                </div>
                <div class="col-12">
                  <label for="inputStateProvince" class="form-label">State / Province</label>
                  <input type="text" class="form-control" id="inputStateProvince" placeholder="State / Province">
                </div>
                <div class="col-xl-4">
                  <label for="inputCountryCode" class="form-label">Country Code</label>
                  <input type="text" class="form-control" id="inputCountryCode" placeholder="Country Code">
                </div>
                <div class="col-xl-8">
                  <label for="inputPhoneNumber" class="form-label">Phone Number</label>
                  <input type="text" class="form-control" id="inputPhoneNumber" placeholder="Phone Number">
                </div>
                <div class="col-12">
                  <ul class="list-unstyled mt-2">
                    <li class="mb-2">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="gridCheck">
                        <label class="form-check-label" for="gridCheck">
                          My billing and delivery information are the same
                        </label>
                      </div>
                    </li>
                    <li>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="gridCheck2">
                        <label class="form-check-label" for="gridCheck2">
                          Please send me emails with exclusive offers and updates
                        </label>
                      </div>
                    </li>
                  </ul>
                </div>
              </form>
            </section>

            <section class="mt-10">
              <h2 class="fw-bold">Billing</h2>
              <ul class="nav nav-pills" id="myTab" role="tablist">
                <li class="nav-item" role="presentation">
                  <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home"
                    type="button" role="tab" aria-controls="home" aria-selected="true">Credit Card</button>
                </li>
                <li class="nav-item" role="presentation">
                  <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button"
                    role="tab" aria-controls="profile" aria-selected="false">Paypal</button>
                </li>
              </ul>
              <div class="tab-content mt-4" id="myTabContent">
                <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
                  <form class="row g-2">
                    <div class="col-12">
                      <label for="inputCardNumber" class="form-label">Card Number</label>
                      <input type="text" class="form-control" id="inputCardNumber" placeholder="Card Number">
                    </div>
                    <div class="col-xl-6">
                      <label for="inputCardName" class="form-label">Name on Card</label>
                      <input type="text" class="form-control" id="inputCardName" placeholder="Name on card">
                    </div>
                    <div class="col-xl-3">
                      <label for="inputCardMonth" class="form-label">Month</label>
                      <input type="text" class="form-control" id="inputCardMonth" placeholder="Month">
                    </div>
                    <div class="col-xl-3">
                      <label for="inputCardCVV" class="form-label">CVV</label>
                      <input type="text" class="form-control" id="inputCardCVV" placeholder="CVV">
                    </div>
                  </form>
                </div>
                <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">...</div>
              </div>
            </section>
          </div>
          <div class="col-xl-5 ps-xl-10">
            <div class="card bg-light sticky-top">
              <div class="accordion accordion-classic" id="accordion-1">
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-1-1">
                    <button class="accordion-button collapsed fw-bold fs-2" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-1-1" aria-expanded="false" aria-controls="collapse-1-1">
                      $249.99
                    </button>
                  </h2>
                  <div id="collapse-1-1" class="accordion-collapse collapse" aria-labelledby="heading-1-1"
                    data-bs-parent="#accordion-1">
                    <div class="accordion-body">
                      <ol class="list-group list-group-minimal">
                        <li class="list-group-item d-flex justify-content-between align-items-start text-secondary">
                          Subtotal
                          <span class="text-black">$35.90</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-start text-secondary">
                          Delivery
                          <span class="text-black">Free</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-start text-secondary">
                          Tax
                          <span class="text-black">$10.00</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-start text-secondary">
                          Insurance
                          <span class="text-black">$5.00</span>
                        </li>
                      </ol>
                    </div>
                  </div>
                </div>
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-1-2">
                    <button class="accordion-button fs-lg collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-1-2" aria-expanded="false" aria-controls="collapse-1-2">
                      Delivery
                    </button>
                  </h2>
                  <div id="collapse-1-2" class="accordion-collapse collapse" aria-labelledby="heading-1-2"
                    data-bs-parent="#accordion-1">
                    <div class="accordion-body">
                      <div class="form-check">
                        <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1"
                          checked>
                        <label class="form-check-label" for="flexRadioDefault1">
                          Standard ( within 2 weeks )
                        </label>
                      </div>
                      <div class="form-check mt-1">
                        <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault2">
                        <label class="form-check-label" for="flexRadioDefault2">
                          Express ( within 2 working days)
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-1-3">
                    <button class="accordion-button fs-lg collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-1-3" aria-expanded="false" aria-controls="collapse-1-3">
                      Cart
                    </button>
                  </h2>
                  <div id="collapse-1-3" class="accordion-collapse collapse" aria-labelledby="heading-1-3"
                    data-bs-parent="#accordion-1">
                    <div class="accordion-body">
                      <ol class="list-group list-group-minimal">
                        <li class="list-group-item d-flex justify-content-between align-items-start text-black">
                          <span>
                            Analog Magazine Rack x2
                            <span class="text-muted d-block">Blue</span>
                          </span>
                          $35.90
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-start text-black">
                          <span>
                            Analog Magazine Rack x2
                            <span class="text-muted d-block">Blue</span>
                          </span>
                          $35.90
                        </li>
                      </ol>
                      <a href="" class="action underline mt-1">Edit Cart <i class="bi bi-arrow-right-short"></i></a>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <div class="d-grid text-center">
                  <a href="" class="btn btn-lg btn-primary rounded-pill">Proceed to Checkout</a>
                  <span class="d-flex justify-content-center align-items-center text-muted mt-2"><i
                      class="bi bi-shield-lock fs-6 me-1"></i>
                    Secure Transaction</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>


    <!-- footer -->
    {{> footer/footer-1
    logo="/assets/images/logo/logo-light.svg"
    class="py-15 py-xl-20 bg-black inverted"
    btnClass="btn-primary"
    }}
  </div>

  <!-- javascript -->
  {{> partials/scripts}}
</body>

</html>