<!doctype html>
<html lang="en">

<head>
  {{> partials/head title="Course"}}
</head>

<style>
  body {
    --bs-primary: #1d4b40;
  }
</style>

<body>



  <!-- navbar -->
  {{> navbar/navbar
  logo="/assets/images/logo/logo-light.svg"
  class="navbar-sticky navbar-dark"
  link="text-white action underline"
  active="course"
  button="btn-outline-white"
  }}


  <!-- header -->
  <section class="bg-green inverted overflow-hidden">
    <div class="container d-flex flex-column py-15 py-xl-20 min-vh-100 level-1">
      <div class="row my-auto justify-content-center align-items-center">
        <div class="col-md-8 col-lg-5 position-relative order-lg-2 mb-5 mb-lg-0" data-aos="fade-up">
          <figure class="equal-1-1 rounded-circle" style="background-image: url('./assets/images/course-1.jpg')">
          </figure>
          <img class="position-absolute bottom-0 end-0 rotate" src="./assets/images/svg/featured-light.svg" alt="">
        </div>
        <div class="col-lg-7 text-center text-lg-start">
          <h1 class="display-1"><span class="fw-bold">Web design</span> &mdash; from beginner to senior.</h1>
          <div class="row justify-content-center justify-content-lg-start g-1">
            <div class="col-auto"><a href="" class="btn btn-white rounded-pill">Start Learning</a></div>
            <div class="col-auto"><a href="" class="btn btn-outline-white rounded-pill">Learn More</a></div>
          </div>
          <div class="row mt-10">
            <div class="col-4">
              <span class="eyebrow text-muted mb-1">tool</span>
              <span class="d-block fs-5">Figma</span>
            </div>
            <div class="col-4">
              <span class="eyebrow text-muted mb-1">level</span>
              <span class="d-block fs-5">Beginner</span>
            </div>
            <div class="col-4">
              <span class="eyebrow text-muted mb-1">duration</span>
              <span class="d-block fs-5">18 Hours</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <figure class="background">
      <svg width="100%" height="100%" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle data-aos="fade-up" data-aos-delay="150" cx="125%" cy="-25%" r="35%" fill="white" fill-opacity=".05"
          data-top-top="@cy: -25%; @cx: 115%;" data-top-bottom="@cy: 0%; @cx: 105%;" />
        <circle data-aos="fade-up" data-aos-delay="300" cx="90%" cy="125%" r="75%" fill="black" fill-opacity=".1"
          data-center-top="@r: 75%;" data-top-bottom="@r: 85%;" />
        <circle data-aos="fade-up" data-aos-delay="450" cx="5%" cy="125%" r="50%" stroke="black" stroke-opacity=".2"
          data-center-top="@r: 50%;" data-center-bottom="@r: 70%;" />
      </svg>
    </figure>
  </section>


  <!-- steps carousel -->
  <section class="py-15 py-xl-20 overflow-hidden">
    <div class="container">
      <div class="row mb-10">
        <div class="col-8">
          <h2>How it works?</h2>
        </div>
      </div>
      <div class="row">
        <div class="col-4">
          <div class="carousel carousel-visible carousel-steps">
            <div data-carousel='{"controls": false, "item" : 1, "nav": false, "loop" : false, "mouseDrag" : true, "gutter"
            : 40}'>
              <div>
                <h4 class="fs-5">Studying</h4>
                <p class="text-secondary fs-lg">Pariatur quia fugit porro autem numquam deserunt, minima aspernatur
                  laboriosam error culpa magni accusantium natus, alias a! Odit, doloremque.</p>
              </div>
              <div>
                <h4 class="fs-5">Completing Tasks</h4>
                <p class="text-secondary fs-lg">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Ea, in? At
                  doloribus quas dolores excepturi?</p>
              </div>
              <div>
                <h4 class="fs-5">Lessons with Teacher</h4>
                <p class="text-secondary fs-lg">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Ea, in? At
                  doloribus quas dolores excepturi?</p>
              </div>
              <div>
                <h4 class="fs-5">Custom Project</h4>
                <p class="text-secondary fs-lg">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Ea, in? At
                  doloribus quas dolores excepturi?</p>
              </div>
              <div>
                <h4 class="fs-5">Finish</h4>
                <p class="text-secondary fs-lg">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Ea, in? At
                  doloribus quas dolores excepturi?</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>


  <section class="bg-light py-15 py-xl-20">
    <div class="container">
      <div class="row mb-5">
        <div class="col-12 text-center">
          <h2 class="fw-bold">What will you learn?</h2>
        </div>
      </div>
      <div class="row g-3 g-xl-5">
        <div class="col-lg-6">
          <div class="card bg-opaque-white" data-aos="fade-up">
            <div class="card-body bg-white">
              <h3 class="fs-4 my-3">Introduction</h3>
              <div class="accordion" id="accordion-1">
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-1-1">
                    <button class="accordion-button lead collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-1-1" aria-expanded="false" aria-controls="collapse-1-1">
                      Welcome to the course
                    </button>
                  </h2>
                  <div id="collapse-1-1" class="accordion-collapse collapse" aria-labelledby="heading-1-1"
                    data-bs-parent="#accordion-1">
                    <div class="accordion-body">
                      <p class="text-secondary">Yes. The gulp workflow can
                        be
                        bypassed all together if you prefer to simply edit the static HTML and CSS files.</p>
                    </div>
                  </div>
                </div>
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-1-2">
                    <button class="accordion-button lead collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-1-2" aria-expanded="false" aria-controls="collapse-1-2">
                      Getting Started
                    </button>
                  </h2>
                  <div id="collapse-1-2" class="accordion-collapse collapse" aria-labelledby="heading-1-2"
                    data-bs-parent="#accordion-1">
                    <div class="accordion-body">
                      <p class="text-secondary">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Illo,
                        voluptas. Quibusdam, quia. Accusantium, quo maxime est ullam at voluptas aspernatur. Iure
                        assumenda labore esse vero ad. Deleniti ea totam dolorem.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-footer">
              <ul class="list-inline list-inline-separated text-secondary eyebrow">
                <li class="list-inline-item">2 Lessons</li>
                <li class="list-inline-item">3.3 Hours</li>
              </ul>
            </div>
          </div>
          <div class="card bg-opaque-white mt-3 mt-xl-5" data-aos="fade-up">
            <div class="card-body bg-white">
              <h3 class="fs-4 my-3">Color</h3>
              <div class="accordion" id="accordion-3">
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-3-1">
                    <button class="accordion-button lead collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-3-1" aria-expanded="false" aria-controls="collapse-3-1">
                      Working with color
                    </button>
                  </h2>
                  <div id="collapse-3-1" class="accordion-collapse collapse" aria-labelledby="heading-3-1"
                    data-bs-parent="#accordion-3">
                    <div class="accordion-body">
                      <p class="text-secondary">Yes. The gulp workflow can
                        be
                        bypassed all together if you prefer to simply edit the static HTML and CSS files.</p>
                    </div>
                  </div>
                </div>
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-3-2">
                    <button class="accordion-button lead collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-3-2" aria-expanded="false" aria-controls="collapse-3-2">
                      How to use the gradient tool in Illustrator
                    </button>
                  </h2>
                  <div id="collapse-3-2" class="accordion-collapse collapse" aria-labelledby="heading-3-2"
                    data-bs-parent="#accordion-3">
                    <div class="accordion-body">
                      <p class="text-secondary">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Illo,
                        voluptas. Quibusdam, quia. Accusantium, quo maxime est ullam at voluptas aspernatur. Iure
                        assumenda labore esse vero ad. Deleniti ea totam dolorem.</p>
                    </div>
                  </div>
                </div>
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-3-3">
                    <button class="accordion-button lead collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-3-3" aria-expanded="false" aria-controls="collapse-3-3">
                      How to use the pen tool in Illustrator
                    </button>
                  </h2>
                  <div id="collapse-3-3" class="accordion-collapse collapse" aria-labelledby="heading-3-3"
                    data-bs-parent="#accordion-3">
                    <div class="accordion-body">
                      <p class="text-secondary">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Illo,
                        voluptas. Quibusdam, quia. Accusantium, quo maxime est ullam at voluptas aspernatur. Iure
                        assumenda labore esse vero ad. Deleniti ea totam dolorem.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-footer">
              <ul class="list-inline list-inline-separated text-secondary eyebrow">
                <li class="list-inline-item">2 Lessons</li>
                <li class="list-inline-item">3.3 Hours</li>
              </ul>
            </div>
          </div>
        </div>
        <div class="col-lg-6">
          <div class="card bg-opaque-white" data-aos="fade-up">
            <div class="card-body bg-white">
              <h3 class="fs-4 my-3">How to Draw in Illustrator</h3>
              <div class="accordion" id="accordion-2">
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-2-1">
                    <button class="accordion-button lead collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-2-1" aria-expanded="false" aria-controls="collapse-2-1">
                      Setting up our document
                    </button>
                  </h2>
                  <div id="collapse-2-1" class="accordion-collapse collapse" aria-labelledby="heading-2-1"
                    data-bs-parent="#accordion-2">
                    <div class="accordion-body">
                      <p class="text-secondary">Yes. The gulp workflow can
                        be
                        bypassed all together if you prefer to simply edit the static HTML and CSS files.</p>
                    </div>
                  </div>
                </div>
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-2-2">
                    <button class="accordion-button lead collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-2-2" aria-expanded="false" aria-controls="collapse-2-2">
                      Drawing ith shapes and lines
                    </button>
                  </h2>
                  <div id="collapse-2-2" class="accordion-collapse collapse" aria-labelledby="heading-2-2"
                    data-bs-parent="#accordion-2">
                    <div class="accordion-body">
                      <p class="text-secondary">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Illo,
                        voluptas. Quibusdam, quia. Accusantium, quo maxime est ullam at voluptas aspernatur. Iure
                        assumenda labore esse vero ad. Deleniti ea totam dolorem.</p>
                    </div>
                  </div>
                </div>
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-2-3">
                    <button class="accordion-button lead collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-2-3" aria-expanded="false" aria-controls="collapse-2-3">
                      Grouping and arranging
                    </button>
                  </h2>
                  <div id="collapse-2-3" class="accordion-collapse collapse" aria-labelledby="heading-2-3"
                    data-bs-parent="#accordion-2">
                    <div class="accordion-body">
                      <p class="text-secondary">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Illo,
                        voluptas. Quibusdam, quia. Accusantium, quo maxime est ullam at voluptas aspernatur. Iure
                        assumenda labore esse vero ad. Deleniti ea totam dolorem.</p>
                    </div>
                  </div>
                </div>
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-2-4">
                    <button class="accordion-button lead collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-2-4" aria-expanded="false" aria-controls="collapse-2-4">
                      Drawing with shape builder tool
                    </button>
                  </h2>
                  <div id="collapse-2-4" class="accordion-collapse collapse" aria-labelledby="heading-2-4"
                    data-bs-parent="#accordion-2">
                    <div class="accordion-body">
                      <p class="text-secondary">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Illo,
                        voluptas. Quibusdam, quia. Accusantium, quo maxime est ullam at voluptas aspernatur. Iure
                        assumenda labore esse vero ad. Deleniti ea totam dolorem.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-footer">
              <ul class="list-inline list-inline-separated text-secondary eyebrow">
                <li class="list-inline-item">2 Lessons</li>
                <li class="list-inline-item">3.3 Hours</li>
              </ul>
            </div>
          </div>
          <div class="card bg-opaque-white mt-3 mt-xl-5" data-aos="fade-up">
            <div class="card-body bg-white">
              <h3 class="fs-4 my-3">Sketching</h3>
              <div class="accordion" id="accordion-4">
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-4-1">
                    <button class="accordion-button lead collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-4-1" aria-expanded="false" aria-controls="collapse-4-1">
                      Can I use static HTML files ?
                    </button>
                  </h2>
                  <div id="collapse-4-1" class="accordion-collapse collapse" aria-labelledby="heading-4-1"
                    data-bs-parent="#accordion-4">
                    <div class="accordion-body">
                      <p class="text-secondary">Yes. The gulp workflow can
                        be
                        bypassed all together if you prefer to simply edit the static HTML and CSS files.</p>
                    </div>
                  </div>
                </div>
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-4-2">
                    <button class="accordion-button lead collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-4-2" aria-expanded="false" aria-controls="collapse-4-2">
                      Grids &amp; Layout
                    </button>
                  </h2>
                  <div id="collapse-4-2" class="accordion-collapse collapse" aria-labelledby="heading-4-2"
                    data-bs-parent="#accordion-4">
                    <div class="accordion-body">
                      <p class="text-secondary">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Illo,
                        voluptas. Quibusdam, quia. Accusantium, quo maxime est ullam at voluptas aspernatur. Iure
                        assumenda labore esse vero ad. Deleniti ea totam dolorem.</p>
                    </div>
                  </div>
                </div>
                <div class="accordion-item">
                  <h2 class="accordion-header" id="heading-4-3">
                    <button class="accordion-button lead collapsed" type="button" data-bs-toggle="collapse"
                      data-bs-target="#collapse-4-3" aria-expanded="false" aria-controls="collapse-4-3">
                      Grids &amp; Layout
                    </button>
                  </h2>
                  <div id="collapse-4-3" class="accordion-collapse collapse" aria-labelledby="heading-4-3"
                    data-bs-parent="#accordion-4">
                    <div class="accordion-body">
                      <p class="text-secondary">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Illo,
                        voluptas. Quibusdam, quia. Accusantium, quo maxime est ullam at voluptas aspernatur. Iure
                        assumenda labore esse vero ad. Deleniti ea totam dolorem.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-footer">
              <ul class="list-inline list-inline-separated text-secondary eyebrow">
                <li class="list-inline-item">2 Lessons</li>
                <li class="list-inline-item">3.3 Hours</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>


  <section class="py-15 py-xl-20 border-bottom">
    <div class="container">
      <div class="row mb-10">
        <div class="col-lg-6">
          <h2 class="fw-light"><span class="fw-bold">A digital studio</span> creating experiences that amaze</h2>
        </div>
      </div>
      <div class="row g-3 g-xl-5">
        <div class="col-lg-4">
          <div class="d-flex">
            <div class="icon-box icon-box-sm bg-opaque-green rounded-circle me-2"><i
                class="bi bi-check2 text-green"></i>
            </div>
            <div>
              <p class="fs-lg">Prepare to learn Data Analysis by understanding how and why it is used.</p>
            </div>
          </div>
        </div>
        <div class="col-lg-4">
          <div class="d-flex">
            <div class="icon-box icon-box-sm bg-opaque-green rounded-circle me-2"><i
                class="bi bi-check2 text-green"></i>
            </div>
            <div>
              <p class="fs-lg">Learn all the syntax you need to create tables in your HTML documents.
                View Details
                Start
              </p>
            </div>
          </div>
        </div>
        <div class="col-lg-4">
          <div class="d-flex">
            <div class="icon-box icon-box-sm bg-opaque-green rounded-circle me-2"><i
                class="bi bi-check2 text-green"></i>
            </div>
            <div>
              <p class="fs-lg">Learn the basics of HTML. HTML is a language that provides structure to website content.
              </p>
            </div>
          </div>
        </div>
        <div class="col-lg-4">
          <div class="d-flex">
            <div class="icon-box icon-box-sm bg-opaque-green rounded-circle me-2"><i
                class="bi bi-check2 text-green"></i>
            </div>
            <div>
              <p class="fs-lg">Get setup to build websites on your own computer!</p>
            </div>
          </div>
        </div>
        <div class="col-lg-4">
          <div class="d-flex">
            <div class="icon-box icon-box-sm bg-opaque-green rounded-circle me-2"><i
                class="bi bi-check2 text-green"></i>
            </div>
            <div>
              <p class="fs-lg">Dig deeper into CSS and improve your ability to layout and style websites.</p>
            </div>
          </div>
        </div>
        <div class="col-lg-4">
          <div class="d-flex">
            <div class="icon-box icon-box-sm bg-opaque-green rounded-circle me-2"><i
                class="bi bi-check2 text-green"></i>
            </div>
            <div>
              <p class="fs-lg">Prepare to learn Data Analysis by understanding how and why it is used.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>


  <!-- pricing -->
  <section class="py-15 py-xl-20">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-lg-6 text-center mb-8">
          <span class="badge bg-opaque-green text-green mb-2 rounded-pill">Pricing Plans</span>
          <h2 class="fw-bold">Get started with CUBE</h2>
          <p class="text-secondary">Incidunt sunt optio aspernatur provident molestias! Vero quidem nihil temporibus,
            assumenda dolor voluptates dolorem tempore voluptas, reprehenderit velit eius ullam error esse.</p>
        </div>
      </div>
      <div class="row g-3 g-xl-5 align-items-end">
        <div class="col-md-6 col-lg-4" data-aos="fade-up">
          <div class="card border border-green text-center">
            <div class="card-body bg-green inverted">
              <span class="fs-lg text-white">One Day</span>
              <h2 class="h1 my-2">$9</h2>
              <p class="text-secondary fs-lg">Lorem ipsum dolor sit amet consectetur adipisicing elit.</p>
              <div class="d-grid mt-5">
                <a href="https://themes.getbootstrap.com/product/cube-multipurpose-template-ui-kit/"
                  class="btn btn-white btn-lg rounded-pill">Buy Now</a>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 col-lg-4" data-aos="fade-up" data-aos-delay="100">
          <div class="card border text-center">
            <div class="card-body">
              <span class="fs-lg text-green">Open Area</span>
              <h2 class="h1 my-2">$39</h2>
              <p class="text-secondary fs-lg">Lorem ipsum dolor sit amet consectetur adipisicing elit.</p>
              <div class="d-grid mt-5">
                <a href="https://themes.getbootstrap.com/product/cube-multipurpose-template-ui-kit/"
                  class="btn btn-outline-green btn-lg rounded-pill">Buy Now</a>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6 col-lg-4" data-aos="fade-up" data-aos-delay="200">
          <div class="card border text-center">
            <div class="card-body">
              <span class="fs-lg text-green">Office</span>
              <h2 class="h1 my-2">$129</h2>
              <p class="text-secondary fs-lg">Lorem ipsum dolor sit amet consectetur adipisicing elit.</p>
              <div class="d-grid mt-5">
                <a href="https://themes.getbootstrap.com/product/cube-multipurpose-template-ui-kit/"
                  class="btn btn-outline-green btn-lg rounded-pill">Buy Now</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>



  <!-- subscribe -->
  <section class="bg-green mx-xl-3 py-15 py-xl-20 inverted overflow-hidden">
    <div class="container level-1">
      <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6 text-center">
          <h2 class="mb-5">Subscribe and get latest entries right to your inbox.
          </h2>
          <div class="grouped-inputs p-1 border rounded-pill mb-2">
            <div class="row g-1">
              <div class="col-lg-9">
                <input type="text" class="form-control px-4 rounded-pill" aria-label="Text input with dropdown button"
                  placeholder="Your email address">
              </div>
              <div class="col-lg-3 d-grid">
                <a href="" class="btn btn-white rounded-pill">Subscribe</a>
              </div>
            </div>
          </div>
          <small class="text-muted">We'll never share your email with anyone else.</small>
        </div>
      </div>
    </div>
    <figure class="background background-dimm background-parallax"
      style="background-image: url('./assets/images/course-2.jpg')" data-bottom-top="transform: translateY(0%);"
      data-top-bottom="transform: translateY(20%);">
    </figure>
  </section>


  <!-- logo list -->
  <section class="py-10">
    <div class="container">
      <div class="carousel carousel-align text-center">
        <div
          data-carousel='{"gutter": 48, "loop": false, "nav": false, "controls": false, "responsive": {"0": {"items": 2}, "768": {"items": 4}, "1200": {"items": 5}}}'>
          <div>
            <img src="./assets/images/logo/logo-1.svg" alt="Logo" class="logo">
          </div>
          <div>
            <img src="./assets/images/logo/logo-2.svg" alt="Logo" class="logo">
          </div>
          <div>
            <img src="./assets/images/logo/logo-3.svg" alt="Logo" class="logo">
          </div>
          <div>
            <img src="./assets/images/logo/logo-4.svg" alt="Logo" class="logo">
          </div>
          <div>
            <img src="./assets/images/logo/logo-5.svg" alt="Logo" class="logo">
          </div>
        </div>
      </div>
    </div>
  </section>


  <!-- footer -->
  {{> footer/footer-2
  class="py-15 py-xl-20 bg-light"
  }}



  <!-- javascript -->
  {{> partials/scripts}}
</body>

</html>