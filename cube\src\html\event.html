<!doctype html>
<html lang="en">

<head>
  {{> partials/head title="Event"}}
</head>

<body>



  <!-- navbar -->
  {{> navbar/navbar
  logo="/assets/images/logo/logo-light.svg"
  class="navbar-sticky navbar-dark"
  active="event"
  button="btn-outline-white"
  }}



  <!-- header -->
  <section class="cover overflow-hidden bg-primary inverted">
    <div class="d-flex flex-column min-vh-100 py-20 container foreground">
      <div class="row justify-content-center my-auto">
        <div class="col-lg-8 col-xl-6">
          <h1 class="display-1 mb-1">Digital Marketing Conference.</h1>
          <div class="text-secondary fs-3">
            <span data-typed='{"strings": ["10 - 11 December, 2021", "New York, USA"]}'></span>
          </div>
        </div>
      </div>
    </div>
    <figure class="background background-dimm" style="background-image: url('./assets/images/event.jpg')"
      data-top-top="transform: translateY(0%);" data-top-bottom="transform: translateY(20%);"></figure>
    <span class="scroll-down"></span>
  </section>


  <!-- logo list -->
  <section class="py-10 border-bottom">
    <div class="container">
      <div class="carousel carousel-align text-center">
        <div
          data-carousel='{"gutter": 48, "loop": false, "nav": false, "controls": false, "responsive": {"0": {"items": 2}, "768": {"items": 4}, "1200": {"items": 5}}}'>
          <div>
            <img src="./assets/images/logo/logo-1.svg" alt="Logo" class="logo">
          </div>
          <div>
            <img src="./assets/images/logo/logo-2.svg" alt="Logo" class="logo">
          </div>
          <div>
            <img src="./assets/images/logo/logo-3.svg" alt="Logo" class="logo">
          </div>
          <div>
            <img src="./assets/images/logo/logo-4.svg" alt="Logo" class="logo">
          </div>
          <div>
            <img src="./assets/images/logo/logo-5.svg" alt="Logo" class="logo">
          </div>
        </div>
      </div>
    </div>
  </section>


  <!-- features -->
  <section class="py-15 py-xl-20">
    <div class="container">
      <div class="row align-items-center justify-content-between">
        <div class="col-md-10 col-xl-5 mb-5 mb-xl-0">
          <h2>We create beautiful, functional spaces</h2>
          <p class="text-secondary lead">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
            incididunt ut labore et
            dolore magna aliqua.</p>
          <a href="" class="action underline">Get Tickets <i class="bi bi-arrow-right"></i></a>
        </div>
        <div class="col-xl-6">
          <div class="row g-3 g-xl-5" data-masonry>
            <div class="col-md-6" data-aos="fade-up">
              <div class="card equal-xl-1-1 bg-primary inverted">
                <div class="card-wrap">
                  <div class="card-header pb-0">
                    <i class="bi bi-clock-fill fs-3"></i>
                  </div>
                  <div class="card-footer mt-auto">
                    <h3 class="fs-4">10-11 Dec</h3>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6 mt-xl-20" data-aos="fade-up">
              <div class="card equal-xl-1-1 inverted">
                <div class="card-wrap">
                  <div class="card-header pb-0">
                    <i class="bi bi-compass-fill fs-3"></i>
                  </div>
                  <div class="card-footer mt-auto">
                    <h3 class="fs-4">New York, USA</h3>
                  </div>
                </div>
                <figure class="background background-overlay"
                  style="background-image: url('./assets/images/newyork.jpg')" data-top-top="transform: translateY(0%);"
                  data-top-bottom="transform: translateY(20%);"></figure>
              </div>
            </div>
            <div class="col-md-6" data-aos="fade-up">
              <div class="card equal-xl-1-1 bg-light">
                <div class="card-wrap">
                  <div class="card-header pb-0">
                    <i class="bi bi-people-fill fs-3"></i>
                  </div>
                  <div class="card-footer mt-auto">
                    <h3 class="fs-4">120 Speakers</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>



  <!-- schedule -->
  <section class="py-20 bg-light">
    <div class="container foreground">
      <div class="row mb-5 justify-content-between align-items-end">
        <div class="col mb-2 mb-md-0">
          <h2 class="lh-1">Schedule</h2>
        </div>
        <div class="col-auto">
          <ul class="nav nav-pills" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home" type="button"
                role="tab" aria-controls="home" aria-selected="true">Tue, 10 Dec</button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile" type="button"
                role="tab" aria-controls="profile" aria-selected="false">Wed, 11 Dec</button>
            </li>
          </ul>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <div class="tab-content" id="myTabContent">
            <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">

              <div class="card bg-white">
                <div class="accordion accordion-classic" id="accordion-1">
                  <div class="accordion-item">
                    <div class="accordion-header" id="heading-1-1">
                      <div class="accordion-button collapsed" role="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse-1-1" aria-expanded="false" aria-controls="collapse-1-1">
                        <div class="d-flex flex-wrap align-items-center w-100">
                          <div class="col-3 col-md-2 text-secondary fs-lg">8:00
                          </div>
                          <div class="col-9 col-md-7 fs-lg">Registration</div>
                          <div class="d-none d-md-block col-md-3 text-md-end pt-1 pt-md-0">
                            <ul class="avatar-list">
                              <li>
                                <span class="avatar" data-bs-toggle="tooltip" data-bs-placement="top"
                                  title="Valerie Doe">
                                  <img src="./assets/images/users/user-1.jpg" class="rounded-circle" alt="Avatar">
                                </span>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="collapse-1-1" class="accordion-collapse collapse" aria-labelledby="heading-1-1"
                      data-bs-parent="#accordion-1">
                      <div class="accordion-body">
                        <div class="d-flex justify-content-end">
                          <div class="col-md-10">
                            <p class="text-secondary">Lorem ipsum dolor sit amet consectetur, adipisicing elit.
                              Blanditiis
                              asperiores sed
                              consectetur
                              placeat ut vitae ad sequi laudantium explicabo cupiditate non, deserunt quas sapiente
                              fuga
                              fugiat a
                              ipsam adipisci odit.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <div class="accordion-header" id="heading-1-2">
                      <div class="accordion-button collapsed" role="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse-1-2" aria-expanded="false" aria-controls="collapse-1-2">
                        <div class="d-flex flex-wrap align-items-center w-100">
                          <div class="col-3 col-md-2 text-secondary fs-lg">9:00
                          </div>
                          <div class="col-9 col-md-7 fs-lg">Opening words from our headliners</div>
                          <div class="d-none d-md-block col-md-3 text-md-end pt-1 pt-md-0">
                            <ul class="avatar-list">
                              <li>
                                <span class="avatar rounded-circle bg-primary">
                                  <span>JD</span>
                                </span>
                              </li>
                              <li>
                                <span class="avatar" data-bs-toggle="tooltip" data-bs-placement="top"
                                  title="Valerie Doe">
                                  <img src="./assets/images/users/user-2.jpg" class="rounded-circle" alt="Avatar">
                                </span>
                              </li>
                              <li>
                                <span class="avatar" data-bs-toggle="tooltip" data-bs-placement="top"
                                  title="Valerie Doe">
                                  <img src="./assets/images/users/user-3.jpg" class="rounded-circle" alt="Avatar">
                                </span>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="collapse-1-2" class="accordion-collapse collapse" aria-labelledby="heading-1-2"
                      data-bs-parent="#accordion-1">
                      <div class="accordion-body">
                        <div class="d-flex justify-content-end">
                          <div class="col-md-10">
                            <p class="text-secondary">Lorem ipsum dolor sit amet consectetur, adipisicing elit.
                              Blanditiis
                              asperiores sed
                              consectetur
                              placeat ut vitae ad sequi laudantium explicabo cupiditate non, deserunt quas sapiente
                              fuga
                              fugiat a
                              ipsam adipisci odit.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="accordion-item">
                    <div class="accordion-header" id="heading-1-3">
                      <div class="accordion-button collapsed" role="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse-1-3" aria-expanded="false" aria-controls="collapse-1-3">
                        <div class="d-flex flex-wrap align-items-center w-100">
                          <div class="col-3 col-md-2 text-secondary fs-lg">10:30
                          </div>
                          <div class="col-9 col-md-7 fs-lg">Master class from our top product designers from Europe
                          </div>
                          <div class="d-none d-md-block col-md-3 text-md-end pt-1 pt-md-0">
                            <ul class="avatar-list">
                              <li>
                                <span class="avatar" data-bs-toggle="tooltip" data-bs-placement="top"
                                  title="Valerie Doe">
                                  <img src="./assets/images/users/user-3.jpg" class="rounded-circle" alt="Avatar">
                                </span>
                              </li>
                              <li>
                                <span class="avatar" data-bs-toggle="tooltip" data-bs-placement="top"
                                  title="Valerie Doe">
                                  <img src="./assets/images/users/user-4.jpg" class="rounded-circle" alt="Avatar">
                                </span>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="collapse-1-3" class="accordion-collapse collapse" aria-labelledby="heading-1-3"
                      data-bs-parent="#accordion-1">
                      <div class="accordion-body">
                        <div class="d-flex justify-content-end">
                          <div class="col-md-10">
                            <p class="text-secondary">Lorem ipsum dolor sit amet consectetur, adipisicing elit.
                              Blanditiis
                              asperiores sed
                              consectetur
                              placeat ut vitae ad sequi laudantium explicabo cupiditate non, deserunt quas sapiente
                              fuga
                              fugiat a
                              ipsam adipisci odit.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
            <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">...</div>
          </div>
        </div>
      </div>
    </div>
  </section>



  <!-- speakers -->
  <section class="py-0">
    <div class="container-full">
      <div class="row g-0">
        <div class="col-sm-6 col-md-6 col-lg-8 col-xl-3">
          <div class="card h-100 bg-primary inverted">
            <div class="card-wrap">
              <div class="card-footer mt-auto">
                <h2>Speakers</h2>
              </div>
            </div>
          </div>
        </div>
        <div class="col-sm-6 col-lg-4 col-xl-3">
          {{> components/team-member
          name="John Doe"
          class="equal-1-1"
          position="Senior Product Designer"
          image="./assets/images/users/user-1.jpg"
          }}
        </div>
        <div class="col-sm-6 col-lg-4 col-xl-3">
          {{> components/team-member
          name="John Doe"
          class="equal-1-1"
          position="Senior Product Designer"
          image="./assets/images/users/user-2.jpg"
          }}
        </div>
        <div class="col-sm-6 col-lg-4 col-xl-3">
          {{> components/team-member
          name="John Doe"
          class="equal-1-1"
          position="Senior Product Designer"
          image="./assets/images/users/user-3.jpg"
          }}
        </div>
        <div class="col-sm-6 col-lg-4 col-xl-3">
          {{> components/team-member
          name="John Doe"
          class="equal-1-1"
          position="Senior Product Designer"
          image="./assets/images/users/user-4.jpg"
          }}
        </div>
        <div class="col-sm-6 col-lg-4 col-xl-3">
          {{> components/team-member
          name="John Doe"
          class="equal-1-1"
          position="Senior Product Designer"
          image="./assets/images/users/user-5.jpg"
          }}
        </div>
        <div class="col-sm-6 col-lg-4 col-xl-3">
          {{> components/team-member
          name="John Doe"
          class="equal-1-1"
          position="Senior Product Designer"
          image="./assets/images/users/user-6.jpg"
          }}
        </div>
        <div class="col-sm-6 col-lg-4 col-xl-3">
          {{> components/team-member
          name="John Doe"
          class="equal-1-1"
          position="Senior Product Designer"
          image="./assets/images/users/user-7.jpg"
          }}
        </div>
      </div>
    </div>
  </section>


  <!-- price -->
  <section class="py-20 mx-0 mx-lg-3">
    <div class="container">
      <div class="row justify-content-center mb-6">
        <div class="col-lg-8 text-center">
          <h2 class="fw-bold mb-1">Get started with CUBE</h2>
          <p class="lead text-secondary">Unlimited possibilities that come at an affordable price.</p>
        </div>
      </div>
      <div class="row g-4 align-items-end">
        <div class="col-lg-4" data-aos="fade-up">
          <div class="card bg-primary bordered inverted">
            <div class="card-body">
              <h2 class="h1 mb-10 fw-bold">$49</h2>
              <ul class="list-unstyled mb-4">
                <li class="py-1">Single site</li>
                <li class="py-1">Use for personal or a client</li>
                <li class="py-1">Use in a free end product <br> (Can have multiple users)</li>
                <li class="py-1 text-muted"><s>Use in an end product that is “sold” <br> (Can have
                    multiple paying
                    users)</s></li>
              </ul>
              <div class="d-grid">
                <a href="https://themes.getbootstrap.com/product/cube-multipurpose-template-ui-kit/"
                  class="btn btn-white btn-with-icon rounded-pill">Buy Now <i class="bi bi-arrow-right"></i></a>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-8" data-aos="fade-up" data-aos-delay="100">
          <div class="row g-0 separated">
            <div class="col-md-6 bg-white">
              <div class="card">
                <div class="card-body">
                  <h2 class="h1 mb-10 fw-bold">$149</h2>
                  <ul class="list-unstyled mb-4">
                    <li class="py-1">Multiple sites</li>
                    <li class="py-1">Use for personal or a client</li>
                    <li class="py-1">Use in a free end product <br> (Can have multiple users)</li>
                    <li class="py-1 text-muted"><s>Use in an end product that is “sold” <br> (Can
                        have multiple paying
                        users)</s></li>
                  </ul>
                  <div class="d-grid">
                    <a href="https://themes.getbootstrap.com/product/cube-multipurpose-template-ui-kit/"
                      class="btn btn-outline-primary btn-with-icon rounded-pill">Buy Now <i
                        class="bi bi-arrow-right"></i></a>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6 bg-white">
              <div class="card">
                <div class="card-body">
                  <h2 class="h1 mb-10 fw-bold">$449</h2>
                  <ul class="list-unstyled mb-4">
                    <li class="py-1">Single site</li>
                    <li class="py-1">Use for personal or a client</li>
                    <li class="py-1">Use in a free end product <br> (Can have multiple users)</li>
                    <li class="py-1">Use in an end product that is “sold” <br> (Can have multiple
                      paying users)</li>
                  </ul>
                  <div class="d-grid">
                    <a href="https://themes.getbootstrap.com/product/cube-multipurpose-template-ui-kit/"
                      class="btn btn-outline-primary btn-with-icon rounded-pill">Buy Now <i
                        class="bi bi-arrow-right"></i></a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>


  <!-- location -->
  <section>
    <div class="container py-20 foreground">
      <div class="row g-0">
        <div class="col-md-6 col-xl-5">
          <div class="card bg-white">
            <div class="card-header">
              <h2>USA, New York, Broadway</h2>
            </div>
            <div class="card-body bg-white pt-0">
              <ul class="list-group list-group-minimal">
                <li class="list-group-item d-flex align-items-center mb-1">
                  <div class="icon-box bg-opaque-primary rounded-circle me-2"><i
                      class="bi bi-pin-angle text-primary"></i>
                  </div>
                  5th Avenue
                </li>
                <li class="list-group-item d-flex align-items-center mb-1">
                  <div class="icon-box bg-opaque-primary rounded-circle me-2"><i
                      class="bi bi-envelope text-primary"></i>
                  </div>
                  <EMAIL>
                </li>
                <li class="list-group-item d-flex align-items-center">
                  <div class="icon-box bg-opaque-primary rounded-circle me-2"><i
                      class="bi bi-telephone text-primary"></i>
                  </div>
                  (*************
                </li>
              </ul>
            </div>
            <div class="card-footer pt-0 text-end">
              <a href="" class="action underline">Find directions <i class="bi bi-arrow-right"></i></a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="media h-100">
      <span id="map1"></span>
    </div>
  </section>


  <!-- footer -->
  {{> footer/footer-2
  class="py-15 py-xl-20 bg-black inverted"
  }}




  <script>
    function initMap() {
      var latlng = new google.maps.LatLng(40.702888, -74.012420);

      var myOptions = {
        zoom: 15,
        center: latlng,
        disableDefaultUI: true,
        styles: [
          {
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#f5f5f5"
              }
            ]
          },
          {
            "elementType": "labels.icon",
            "stylers": [
              {
                "visibility": "off"
              }
            ]
          },
          {
            "elementType": "labels.text.fill",
            "stylers": [
              {
                "color": "#616161"
              }
            ]
          },
          {
            "elementType": "labels.text.stroke",
            "stylers": [
              {
                "color": "#f5f5f5"
              }
            ]
          },
          {
            "featureType": "administrative.land_parcel",
            "elementType": "labels.text.fill",
            "stylers": [
              {
                "color": "#bdbdbd"
              }
            ]
          },
          {
            "featureType": "poi",
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#eeeeee"
              }
            ]
          },
          {
            "featureType": "poi",
            "elementType": "labels.text.fill",
            "stylers": [
              {
                "color": "#757575"
              }
            ]
          },
          {
            "featureType": "poi.park",
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#e5e5e5"
              }
            ]
          },
          {
            "featureType": "poi.park",
            "elementType": "labels.text.fill",
            "stylers": [
              {
                "color": "#9e9e9e"
              }
            ]
          },
          {
            "featureType": "road",
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#ffffff"
              }
            ]
          },
          {
            "featureType": "road.arterial",
            "elementType": "labels.text.fill",
            "stylers": [
              {
                "color": "#757575"
              }
            ]
          },
          {
            "featureType": "road.highway",
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#dadada"
              }
            ]
          },
          {
            "featureType": "road.highway",
            "elementType": "labels.text.fill",
            "stylers": [
              {
                "color": "#616161"
              }
            ]
          },
          {
            "featureType": "road.local",
            "elementType": "labels.text.fill",
            "stylers": [
              {
                "color": "#9e9e9e"
              }
            ]
          },
          {
            "featureType": "transit.line",
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#e5e5e5"
              }
            ]
          },
          {
            "featureType": "transit.station",
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#eeeeee"
              }
            ]
          },
          {
            "featureType": "water",
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#c9c9c9"
              }
            ]
          },
          {
            "featureType": "water",
            "elementType": "labels.text.fill",
            "stylers": [
              {
                "color": "#9e9e9e"
              }
            ]
          }
        ]
      };

      var map = new google.maps.Map(document.getElementById("map1"), myOptions);

      map.panBy(-100, -40);

      var myMarker = new google.maps.Marker(
        {
          position: latlng,
          map: map,
          icon: 'assets/images/svg/pin.svg'
        });
    }
  </script>
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAME5wJgLdn1aQSxC7-iWxJ3isuveK9Rv4&callback=initMap"
    async defer></script>



  <!-- javascript -->
  {{> partials/scripts}}
</body>

</html>