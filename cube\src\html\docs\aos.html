<!doctype html>
<html lang="en">

<head>
  {{> partials/head title="Shadows - Documentation"}}
</head>

<body class="bg-light">


  <!-- navbar -->
  {{> navbar/navbar
  logo="/assets/images/logo/logo-dark.svg"
  class="navbar-sticky navbar-light bg-light border-bottom"
  active="components"
  button="btn-primary"
  }}


  <div class="offcanvas-wrap">
    <section class="split">
      <div class="container">
        <div class="row justify-content-between">



          <div class="col-lg-3 split-sidebar">
            <!-- table of contents-->
            {{> components/toc
            section="utilities"
            active="aos"
            }}
          </div>


          <!-- content -->
          <div class="col-lg-9 split-content">


            <!-- text -->
            <section class="pb-5 border-bottom">
              <h1 class="mb-1">Animate on Scroll</h1>
              <a href="https://michalsnik.github.io/aos/" class="underline action">AOS Documentation <i
                  class="bi bi-arrow-up-right"></i></a>
            </section>


            <!-- example -->
            <section>
              <h3 class="fs-4">Example</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white overflow-hidden">
                  <div class="row justify-content-center g-lg-3">
                    <div class="col-8" data-aos="fade-up">
                      <div class="equal-2-1 bg-primary inverted">
                        <div class="d-flex align-items-center justify-content-center">
                          <pre>&lt;div data-aos="fade-up">&lt;/div></pre>
                        </div>
                      </div>
                    </div>
                    <div class="col-8" data-aos="fade-down">
                      <div class="equal-2-1 bg-green inverted">
                        <div class="d-flex align-items-center justify-content-center">
                          <pre>&lt;div data-aos="fade-down">&lt;/div></pre>
                        </div>
                      </div>
                    </div>
                    <div class="col-8" data-aos="fade-right">
                      <div class="equal-2-1 bg-red inverted">
                        <div class="d-flex align-items-center justify-content-center">
                          <pre>&lt;div data-aos="fade-right">&lt;/div></pre>
                        </div>
                      </div>
                    </div>
                    <div class="col-8" data-aos="fade-left">
                      <div class="equal-2-1 bg-purple inverted">
                        <div class="d-flex align-items-center justify-content-center">
                          <pre>&lt;div data-aos="fade-left">&lt;/div></pre>
                        </div>
                      </div>
                    </div>
                    <div class="col-8" data-aos="flip-left">
                      <div class="equal-2-1 bg-purple inverted">
                        <div class="d-flex align-items-center justify-content-center">
                          <pre>&lt;div data-aos="flip-left">&lt;/div></pre>
                        </div>
                      </div>
                    </div>
                    <div class="col-8" data-aos="flip-right">
                      <div class="equal-2-1 bg-purple inverted">
                        <div class="d-flex align-items-center justify-content-center">
                          <pre>&lt;div data-aos="flip-right">&lt;/div></pre>
                        </div>
                      </div>
                    </div>
                    <div class="col-8" data-aos="flip-up">
                      <div class="equal-2-1 bg-purple inverted">
                        <div class="d-flex align-items-center justify-content-center">
                          <pre>&lt;div data-aos="flip-up">&lt;/div></pre>
                        </div>
                      </div>
                    </div>
                    <div class="col-8" data-aos="flip-down">
                      <div class="equal-2-1 bg-purple inverted">
                        <div class="d-flex align-items-center justify-content-center">
                          <pre>&lt;div data-aos="flip-down">&lt;/div></pre>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card-footer text-center">
                  <a href="https://michalsnik.github.io/aos/" class="btn btn-primary btn-with-icon rounded-pill">View
                    All Animations <i class="bi bi-arrow-up-right"></i></a>
                </div>
              </div>
            </section>


          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- table of contents - mobile-->
  {{> components/toc-mobile }}


  <!-- javascript -->
  {{> partials/scripts}}
</body>

</html>