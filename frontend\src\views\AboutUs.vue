<template>
  <div class="about-us-page">
    <!-- Hero Section -->
    <section class="hero-section overflow-hidden inverted">
      <div class="d-flex flex-column py-15 min-vh-75 container foreground">
        <div class="row justify-content-center my-auto">
          <div class="col-lg-10 text-center hero-content">
            <span class="eyebrow mb-3 text-warm-sunset" data-aos="fade-up">About Make It Home</span>
            <h1 class="fw-bold display-3 mb-4 hero-title" data-aos="fade-up" data-aos-delay="200">
              We're not a <span class="text-warm-sunset">real estate company.</span><br>
              We're a <span class="text-warm-sunset">movement</span> disguised as one.
            </h1>
            <p class="lead mb-4 text-light" data-aos="fade-up" data-aos-delay="400">
              Built from the ground up with a deep belief that homes can change lives
            </p>
            
            <!-- Scroll Indicator -->
            <div class="scroll-indicator-container" data-aos="fade-up" data-aos-delay="600">
              <p class="scroll-text mb-3">Discover Our Story</p>
              <div class="scroll-arrow" @click="scrollToStory">
                <i class="fas fa-chevron-down"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Animated Background -->
      <figure class="background background-overlay animated-bg">
        <div class="bg-layer bg-layer-1"></div>
        <div class="bg-layer bg-layer-2"></div>
        <div class="bg-layer bg-layer-3"></div>
        <div class="floating-elements">
          <div class="floating-element floating-element-1"></div>
          <div class="floating-element floating-element-2"></div>
          <div class="floating-element floating-element-3"></div>
        </div>
      </figure>
      
      <!-- Enhanced smooth transition element -->
      <div class="hero-transition"></div>
    </section>

    <!-- Our Story Section -->
    <section id="our-story" class="py-xl-20 py-15 story-statement-bg">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-lg-10">
            <div class="text-center mb-5" data-aos="fade-up">
              <h2 class="display-3 fw-bold mb-4 text-white story-title">
                {{ content.about.storyTitle || 'Our Story' }}
              </h2>
              <div class="story-statement">
                <p class="fs-4 mb-4 story-statement-text" v-html="content.about.story || defaultStoryContent"></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Our Mission Section -->
    <section class="py-xl-30 py-25 mission-bg inverted">
      <div class="container">
        <div class="row align-items-center">
          <!-- Mission Text Column -->
          <div class="col-lg-7 mb-5 mb-lg-0" data-aos="fade-right">
            <h2 class="display-3 fw-bold mb-6 text-white">Our Mission</h2>
            <div class="mission-content">
              <blockquote class="mission-quote-new">
                <p class="mission-text-new text-white mb-4">
                  "Our mission is to rebuild, reimagine, and reinvest in the Omaha community by creating homes people are proud to live in."
                </p>
              </blockquote>
              <div class="mission-details">
                <p class="text-light fs-5 mb-0 lh-lg">
                  We believe every family deserves a home that reflects their worth and contributes to a thriving community. Through thoughtful renovation and community investment, we're building more than houses, we're building hope.
                </p>
              </div>
            </div>
          </div>

          <!-- Mission Image Column -->
          <div class="col-lg-5" data-aos="fade-left" data-aos-delay="200">
            <div class="mission-image-container">
              <img v-if="content.aboutUs && content.aboutUs.missionImage"
                   :src="getImageUrl(content.aboutUs.missionImage)"
                   alt="Our Mission"
                   class="mission-image">
              <div v-else class="mission-placeholder">
                <i class="fas fa-home fa-4x text-warm-sunset mb-3"></i>
                <p class="text-light mb-0">Mission Image</p>
                <small class="text-muted">Upload via admin panel</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Our Values Section -->
    <section class="py-xl-20 py-15 values-section" v-if="content.values && (content.values.valuesList.length > 0 || content.values.description)">
      <div class="container">
        <div class="row justify-content-center mb-5">
          <div class="col-lg-8 text-center" data-aos="fade-up">
            <h2 class="display-5 fw-bold mb-4 text-dark">{{ content.values.title || 'Our Values' }}</h2>
            <p class="lead text-dark mb-5" v-if="content.values.description">{{ content.values.description }}</p>
          </div>
        </div>

        <div class="row g-4 justify-content-center" v-if="content.values.valuesList && content.values.valuesList.length > 0">
          <div v-for="(value, index) in content.values.valuesList" :key="index" class="col-md-6 col-lg-4" data-aos="fade-up" :data-aos-delay="index * 100">
            <div class="values-card h-100">
              <div class="values-icon mb-3">
                <i :class="value.icon + ' fa-3x'"></i>
              </div>
              <h4 class="values-title">{{ value.title }}</h4>
              <p class="values-description">{{ value.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>



    <!-- Closing Statement Section -->
    <section class="py-xl-20 py-15 closing-section">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-lg-10 text-center" data-aos="fade-up">
            <p class="fs-3 text-dark mb-5 lh-lg fw-light">
              There's no corporate gloss here, just grit, love, and a relentless work ethic that fuels our flips, funds our future, and helps our people make it home.
            </p>
            
            <div class="cta-buttons" data-aos="fade-up" data-aos-delay="200">
              <router-link to="/properties" class="btn btn-sunset-orange btn-lg me-3 mb-3">
                View Our Properties
              </router-link>
              <router-link to="/contact" class="btn btn-outline-dark btn-lg mb-3">
                Get In Touch
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'AboutUs',
  data() {
    return {
      content: {
        about: {
          storyTitle: 'Our Story',
          story: '',
          mission: ''
        },
        aboutUs: {
          missionImage: ''
        },
        values: {
          title: 'Our Values',
          description: '',
          valuesList: []
        }
      },
      defaultStoryContent: `
        <p class="fs-4 text-dark mb-4 lh-lg">
          Make It Home, LLC was built from the ground up, not just with nails and drywall, but with a deep belief that homes can change lives. We aren't interested in flipping houses for fast cash or renting units just to fill space. We are builders of opportunity, architects of change, and fierce believers in what happens when people are finally given a space that reflects their worth.
        </p>

        <p class="fs-4 text-dark mb-4 lh-lg">
          We buy properties that others overlook, homes with history, edges, and flaws, and we breathe new life into them. Then, we hand those homes back to the community, one buyer or renter at a time. The result is a property that's no longer forgotten. A tenant who finally feels proud to walk through their first door. A neighborhood that starts to rise again, one house at a time.
        </p>
      `
    }
  },
  async mounted() {
    document.title = 'About Us - Make It Home'
    await this.loadContent()
  },
  methods: {
    async loadContent() {
      try {
        const response = await axios.get('/api/admin/content/public')
        this.content = response.data || {
          about: { storyTitle: 'Our Story', story: '', mission: '' },
          aboutUs: { missionImage: '' },
          values: { title: 'Our Values', description: '', valuesList: [] }
        }

        // Ensure about structure exists
        if (!this.content.about) {
          this.content.about = { storyTitle: 'Our Story', story: '', mission: '' }
        }

        // Ensure aboutUs structure exists
        if (!this.content.aboutUs) {
          this.content.aboutUs = { missionImage: '' }
        }

        // Ensure values structure exists
        if (!this.content.values) {
          this.content.values = { title: 'Our Values', description: '', valuesList: [] }
        }
      } catch (error) {
        console.error('Error loading content:', error)
        this.content = {
          about: { storyTitle: 'Our Story', story: '', mission: '' },
          aboutUs: { missionImage: '' },
          values: { title: 'Our Values', description: '', valuesList: [] }
        }
      }
    },
    getImageUrl(imagePath) {
      if (!imagePath) return ''
      if (imagePath.startsWith('http')) return imagePath
      return `${import.meta.env.VITE_API_URL || 'http://localhost:5000'}${imagePath}`
    },
    scrollToStory() {
      const element = document.getElementById('our-story')
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }
}
</script>

<style scoped>
/* Hero Section Styles */
.hero-section {
  position: relative;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  min-height: 75vh;
  display: flex;
  align-items: center;
}

.hero-content {
  padding-top: 80px;
  z-index: 10;
  position: relative;
}

/* Hero Title Enhanced Visibility */
.hero-title {
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7), 0 0 20px rgba(0, 0, 0, 0.5);
  color: #ffffff !important;
  font-weight: 800 !important;
  letter-spacing: -0.02em;
}

.hero-title .text-warm-sunset {
  color: #EBA472 !important;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8), 0 0 25px rgba(235, 164, 114, 0.3);
}

@media (max-width: 768px) {
  .hero-content {
    padding-top: 60px;
  }

  .hero-title {
    font-size: 2.5rem !important;
    line-height: 1.2;
  }
}

/* Animated Background */
.animated-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.bg-layer {
  position: absolute;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle at center, rgba(235, 164, 114, 0.1) 0%, transparent 70%);
  animation: float-slow 20s ease-in-out infinite;
}

.bg-layer-2 {
  animation-duration: 15s;
  animation-delay: -5s;
  background: radial-gradient(circle at 30% 70%, rgba(212, 147, 94, 0.08) 0%, transparent 60%);
}

.bg-layer-3 {
  animation-duration: 12s;
  animation-delay: -10s;
  background: radial-gradient(circle at 70% 30%, rgba(235, 164, 114, 0.06) 0%, transparent 50%);
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-element {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(235, 164, 114, 0.1) 0%, transparent 70%);
  animation: float-slow 8s ease-in-out infinite;
}

.floating-element-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-element-2 {
  top: 60%;
  right: 15%;
  animation-delay: -3s;
  animation-duration: 10s;
}

.floating-element-3 {
  bottom: 30%;
  left: 60%;
  animation-delay: -6s;
  animation-duration: 12s;
}

/* Scroll Indicator */
.scroll-indicator-container {
  margin-top: 2rem;
}

.scroll-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  animation: fade-pulse 2s ease-in-out infinite;
}

.scroll-arrow {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #EBA472 0%, #D4935E 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: bounce 2s infinite;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 0 20px rgba(235, 164, 114, 0.3);
}

.scroll-arrow:hover {
  transform: translateY(-5px) scale(1.1);
  animation: pulse-glow 3s infinite;
}

.scroll-arrow i {
  color: white;
  font-size: 1.2rem;
}

/* Enhanced Hero Transition - blends with much darker animated background */
.hero-transition {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 150px;
  background: linear-gradient(to bottom,
    transparent 0%,
    rgba(73, 80, 87, 0.1) 15%,
    rgba(52, 58, 64, 0.2) 25%,
    rgba(33, 37, 41, 0.3) 35%,
    rgba(52, 58, 64, 0.5) 50%,
    rgba(73, 80, 87, 0.7) 70%,
    rgba(52, 58, 64, 0.9) 85%,
    #495057 100%);
  z-index: 5;
}

/* Story Section Enhanced Background */
.story-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
  position: relative;
}

.story-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: linear-gradient(to bottom,
    rgba(26, 26, 26, 0.05) 0%,
    transparent 100%);
  z-index: 1;
}

.story-section .container {
  position: relative;
  z-index: 2;
}

/* Values Section Enhanced Background */
.values-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ffffff 100%);
  position: relative;
}

/* Closing Section Enhanced Background */
.closing-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #f8f9fa 100%);
  position: relative;
}

/* Content Sections */
.divider {
  width: 60px;
  height: 4px;
  background: linear-gradient(to right, #EBA472, #D4935E);
  border-radius: 2px;
}

.story-content p {
  text-align: justify;
  line-height: 1.8;
}

/* Story Statement Background - much darker with enhanced animations */
.story-statement-bg {
  background: linear-gradient(135deg, #495057 0%, #343a40 25%, #212529 50%, #343a40 75%, #495057 100%);
  position: relative;
  overflow: hidden;
}

/* Enhanced animated floating elements */
.story-statement-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(235, 164, 114, 0.25) 0%, transparent 60%),
    radial-gradient(circle at 80% 70%, rgba(212, 147, 94, 0.20) 0%, transparent 55%),
    radial-gradient(circle at 50% 20%, rgba(235, 164, 114, 0.18) 0%, transparent 50%),
    radial-gradient(circle at 30% 80%, rgba(255, 193, 7, 0.15) 0%, transparent 45%);
  animation: floatingGlow 6s ease-in-out infinite;
  pointer-events: none;
}

/* Subtle static geometric patterns - no moving rectangles */
.story-statement-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 100px,
      rgba(235, 164, 114, 0.03) 101px,
      rgba(235, 164, 114, 0.03) 102px
    ),
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 150px,
      rgba(255, 193, 7, 0.02) 151px,
      rgba(255, 193, 7, 0.02) 152px
    );
  pointer-events: none;
}

/* Gentle floating particles animation - no scaling or rotation */
@keyframes floatingGlow {
  0%, 100% {
    transform: translateY(0px);
    opacity: 1;
  }
  25% {
    transform: translateY(-10px);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-5px);
    opacity: 1.2;
  }
  75% {
    transform: translateY(5px);
    opacity: 0.9;
  }
}



/* Story Title - enhanced visibility */
.story-title {
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.5),
    0 4px 8px rgba(0, 0, 0, 0.3),
    0 1px 0 rgba(235, 164, 114, 0.3);
  position: relative;
  z-index: 15;
}

/* Story Statement - solid light background against darker animated background */
.story-statement {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 10;
}

.story-statement-text {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  font-size: 1.25rem !important;
  line-height: 1.6 !important;
  text-align: center !important;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  padding: 3rem 2.5rem;
  border-radius: 24px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.25),
    0 10px 30px rgba(0, 0, 0, 0.15),
    0 5px 15px rgba(235, 164, 114, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.story-statement-text:hover {
  transform: translateY(-4px) scale(1.02);
  backdrop-filter: blur(25px) saturate(200%);
  -webkit-backdrop-filter: blur(25px) saturate(200%);
  background: rgba(255, 255, 255, 0.2);
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.3),
    0 15px 40px rgba(0, 0, 0, 0.2),
    0 8px 25px rgba(235, 164, 114, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Override any pasted formatting to maintain consistency */
.story-statement-text * {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  font-size: 1.25rem !important;
  line-height: 1.6 !important;
  color: inherit !important;
  text-decoration: none !important;
  background: none !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.story-statement-text p {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  font-size: 1.25rem !important;
  line-height: 1.6 !important;
  margin-bottom: 1.5rem !important;
  color: inherit !important;
}

.story-statement-text p:last-child {
  margin-bottom: 0 !important;
}

/* Remove any bold, italic, or other formatting from pasted content */
.story-statement-text strong,
.story-statement-text b {
  font-weight: normal !important;
}

.story-statement-text em,
.story-statement-text i {
  font-style: normal !important;
}

.story-statement-text span {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  font-size: 1.25rem !important;
  color: inherit !important;
  background: none !important;
}

/* Values Cards */
.values-card {
  background: white;
  padding: 2.5rem 2rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(235, 164, 114, 0.1);
}

.values-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.values-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #EBA472 0%, #D4935E 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.values-icon i {
  font-size: 2rem;
  color: white;
}

.values-title {
  color: #1a1a1a;
  font-weight: 700;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.values-description {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 0;
}

/* Enhanced Mission Section */
.mission-bg {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  position: relative;
  margin: 0;
}

/* New Mission Content Styling */
.mission-content {
  padding-right: 3rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.mission-quote-new {
  border: none;
  margin: 0 0 3rem 0;
  padding: 0;
  background: none;
  border-left: 4px solid #EBA472;
  padding-left: 2.5rem;
}

.mission-text-new {
  font-size: 2rem;
  font-style: italic;
  line-height: 1.4;
  font-weight: 300;
  margin-bottom: 2rem;
}

.mission-details {
  padding-left: 2.5rem;
  margin-top: 1.5rem;
}

/* Mission Image Styling */
.mission-image-container {
  position: relative;
  height: 500px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.mission-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 20px;
  transition: transform 0.3s ease;
}

.mission-image:hover {
  transform: scale(1.05);
}

.mission-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(235, 164, 114, 0.3);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  backdrop-filter: blur(10px);
}

.text-warm-sunset {
  color: #EBA472 !important;
}

/* Responsive Design */
@media (max-width: 991px) {
  .mission-content {
    padding-right: 0;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    margin-bottom: 3rem;
  }

  .mission-text-new {
    font-size: 1.75rem;
  }

  .mission-image-container {
    height: 400px;
    margin-top: 1rem;
    margin-bottom: 1rem;
  }
}

@media (max-width: 768px) {
  .mission-quote-new {
    padding-left: 2rem;
    border-left-width: 3px;
    margin-bottom: 2.5rem;
  }

  .mission-text-new {
    font-size: 1.5rem;
  }

  .mission-details {
    padding-left: 2rem;
  }

  .mission-image-container {
    height: 350px;
  }

  .mission-content {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

/* CTA Buttons */
.cta-buttons .btn {
  min-width: 200px;
  padding: 1rem 2rem;
  font-weight: 600;
  border-radius: 50px;
  transition: all 0.3s ease;
}

.btn-sunset-orange {
  background: linear-gradient(135deg, #EBA472 0%, #D4935E 100%);
  border: none;
  color: white;
}

.btn-sunset-orange:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(235, 164, 114, 0.4);
  color: white;
}

.btn-outline-dark:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes float-slow {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(2deg);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
  60% {
    transform: translateY(-4px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 0 20px rgba(235, 164, 114, 0.3);
  }
  50% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 0 30px rgba(235, 164, 114, 0.6);
  }
}

@keyframes fade-pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .scroll-arrow {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .scroll-text {
    font-size: 0.75rem;
  }
  
  .values-card {
    padding: 2rem 1.5rem;
  }

  .values-icon {
    width: 60px;
    height: 60px;
  }

  .values-icon i {
    font-size: 1.5rem;
  }
  
  .cta-buttons .btn {
    min-width: 180px;
    margin-bottom: 1rem;
  }
}
</style>
