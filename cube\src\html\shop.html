<!doctype html>
<html lang="en">

<head>
  {{> partials/head title="Shop"}}
</head>

<body>


  <!-- navbar -->
  {{> navbar/navbar
  logo="/assets/images/logo/logo-light.svg"
  class="navbar-sticky navbar-dark"
  active="shop"
  shop="true"
  button="btn-outline-white"
  account="true"
  }}


  <div class="offcanvas-wrap">

    <!-- hero -->
    <section class="cover overflow-hidden inverted bg-black">
      <div class="d-flex flex-column min-vh-75 container py-10 level-1">
        <div class="row justify-content-center my-auto">
          <div class="col-md-8 text-center">
            <span class="eyebrow text-secondary">New Collection</span>
            <h1 class="display-2 lh-sm fw-bold">Spring Forward.
            </h1>
            <a href="" class="btn btn-outline-white rounded-pill">Shop Collection</a>
          </div>
        </div>
        <div class="row text-center">
          <div class="col">
            <small class="text-secondary">
              Limited time only. Selected styles marked down on site.
            </small>
          </div>
        </div>
      </div>
      <div class="background background-overlay" data-aos="zoom-out" data-aos-delay="200">
        <video data-video playsinline autoplay muted loop>
          <source src="./assets/video/video-2.mp4" type="video/mp4" />
        </video>
      </div>
    </section>


    <!-- product carousel -->
    <section class="overflow-hidden pt-3 pt-xl-4">
      <div class="container">
        <div class="carousel carousel-visible">
          <div
            data-carousel='{"nav": false,"mouseDrag": true, "gutter": 32, "loop": true, "responsive": {"0": {"items": 1}, "768": {"items": 2}, "992": {"items": 2}, "1200": {"items": 3}}}'>
            <div>
              {{> components/product
              (object
              title="Watch"
              price="$100"
              image=( array "assets/images/products/product-1.jpg" "assets/images/products/product-1-2.jpg")
              )
              }}
            </div>
            <div>
              {{> components/product
              (object
              title="Black Karlo Backpack"
              price="$88"
              image=( array "assets/images/products/product-2.jpg" "assets/images/products/product-2-2.jpg")
              )
              }}
            </div>
            <div>
              {{> components/product
              (object
              title="Black Closca Helmet"
              price="$132"
              image=( array "assets/images/products/product-3.jpg" "assets/images/products/product-3-2.jpg")
              )
              }}
            </div>
            <div>
              {{> components/product
              (object
              title="Speaker"
              price="$100"
              image=( array "assets/images/products/product-4.jpg" "assets/images/products/product-4-2.jpg")
              )
              }}
            </div>
            <div>
              {{> components/product
              (object
              title="Gravel Black Sigg Water Bottle"
              price="$23"
              discount="$34"
              image=( array "assets/images/products/product-5.jpg" "assets/images/products/product-5-2.jpg")
              )
              }}
            </div>
          </div>
        </div>
      </div>
    </section>


    <!-- categories -->
    <section class="py-15 py-xl-20">
      <div class="container">
        <div class="row mb-5">
          <div class="col text-center">
            <h2 class="fw-bold mb-0">Categories</h2>
          </div>
        </div>
        <div class="row g-3 g-xl-4" data-masonry>
          <div class="col-md-6" data-aos="fade-up">
            <a href="" class="card equal-md-1-1 card-hover-gradient">
              <div class="card-wrap">
                <div class="card-footer my-auto">
                  <div class="text-center text-shadow">
                    <h3 class="mb-0 fw-bold text-white">Pullovers</h3>
                  </div>
                </div>
              </div>
              <figure class="background" style="background-image: url('./assets/images/shop-1.jpg')"></figure>
            </a>
          </div>
          <div class="col-md-6" data-aos="fade-up">
            <a href="" class="card equal-md-16-9 card-hover-gradient">
              <div class="card-wrap">
                <div class="card-footer my-auto">
                  <div class="text-center text-shadow">
                    <h3 class="mb-0 fw-bold text-white">Trainers</h3>
                  </div>
                </div>
              </div>
              <figure class="background" style="background-image: url('./assets/images/shop-2.jpg')"></figure>
            </a>
          </div>
          <div class="col-md-6" data-aos="fade-up">
            <a href="" class="card equal-md-1-1 card-hover-gradient">
              <div class="card-wrap">
                <div class="card-footer my-auto">
                  <div class="text-center text-shadow">
                    <h3 class="mb-0 fw-bold text-white">Jeans</h3>
                  </div>
                </div>
              </div>
              <figure class="background" style="background-image: url('./assets/images/shop-3.jpg')"></figure>
            </a>
          </div>
          <div class="col-md-6" data-aos="fade-up">
            <a href="" class="card equal-md-16-9 card-hover-gradient">
              <div class="card-wrap">
                <div class="card-footer my-auto">
                  <div class="text-center text-shadow">
                    <h3 class="mb-0 fw-bold text-white">Accessories</h3>
                  </div>
                </div>
              </div>
              <figure class="background" style="background-image: url('./assets/images/shop-4.jpg')"></figure>
            </a>
          </div>
        </div>
      </div>
    </section>


    <!-- product carousel -->
    <section class="overflow-hidden">
      <div class="container">
        <div class="row align-items-end mb-5">
          <div class="col-lg-8 mb-2 mb-lg-0">
            <h2 class="fw-bold">Gadgets</h2>
          </div>
          <div class="col-lg-4 text-lg-end">
            <a href="" class="underline action">View all products <i class="bi bi-arrow-right"></i></a>
          </div>
        </div>
        <div class="carousel carousel-visible" data-aos="fade-left">
          <div
            data-carousel='{"nav": false,"mouseDrag": true, "gutter": 32, "loop": true, "responsive": {"0": {"items": 1}, "768": {"items": 2}, "992": {"items": 2}, "1200": {"items": 3}}}'>
            <div>
              {{> components/product
              (object
              title="Watch"
              price="$100"
              image=( array "assets/images/products/product-1.jpg" "assets/images/products/product-1-2.jpg")
              )
              }}
            </div>
            <div>
              {{> components/product
              (object
              title="Black Karlo Backpack"
              price="$88"
              image=( array "assets/images/products/product-2.jpg" "assets/images/products/product-2-2.jpg")
              )
              }}
            </div>
            <div>
              {{> components/product
              (object
              title="Black Closca Helmet"
              price="$132"
              image=( array "assets/images/products/product-3.jpg" "assets/images/products/product-3-2.jpg")
              )
              }}
            </div>
            <div>
              {{> components/product
              (object
              title="Speaker"
              price="$100"
              image=( array "assets/images/products/product-4.jpg" "assets/images/products/product-4-2.jpg")
              )
              }}
            </div>
            <div>
              {{> components/product
              (object
              title="Gravel Black Sigg Water Bottle"
              price="$23"
              discount="$34"
              image=( array "assets/images/products/product-5.jpg" "assets/images/products/product-5-2.jpg")
              )
              }}
            </div>
          </div>
        </div>
      </div>
    </section>


    <!-- text -->
    <section class="py-15 py-xl-20">
      <div class="container">
        <div class="row g-5 justify-content-center">
          <div class="col-8 col-md-4 text-center">
            <i class="bi bi-truck fs-4 mb-2"></i>
            <h6 class="mb-0 fw-normal fs-lg">Free Shipping</h6>
            <p class="text-muted">From all orders over $100</p>
          </div>
          <div class="col-8 col-md-4 text-center">
            <i class="bi bi-arrow-left-right fs-4 mb-2"></i>
            <h6 class="mb-0 fw-normal fs-lg">Free Returns</h6>
            <p class="text-muted">Return money within 30 days</p>
          </div>
          <div class="col-8 col-md-4 text-center">
            <i class="bi bi-bag-check fs-4 mb-2"></i>
            <h6 class="mb-0 fw-normal fs-lg">Secure Shopping</h6>
            <p class="text-muted">You’re in safe hands</p>
          </div>
        </div>
      </div>
    </section>


    <!-- footer -->
    {{> footer/footer-1
    logo="/assets/images/logo/logo-dark.svg"
    class="py-15 py-xl-20 border-top"
    btnClass="btn-primary"
    }}
  </div>


  <!-- javascript -->
  {{> partials/scripts}}
</body>

</html>