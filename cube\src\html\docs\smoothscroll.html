<!doctype html>
<html lang="en">

<head>
  {{> partials/head title="Smoothscroll - Documentation"}}
</head>

<body class="bg-light">


  <!-- navbar -->
  {{> navbar/navbar
  logo="/assets/images/logo/logo-dark.svg"
  class="navbar-sticky navbar-light bg-light border-bottom"
  active="components"
  button="btn-primary"
  }}


  <div class="offcanvas-wrap">
    <section class="split">
      <div class="container">
        <div class="row justify-content-between">



          <div class="col-lg-3 split-sidebar">
            <!-- table of contents-->
            {{> components/toc
            section="components"
            active="smoothscroll"
            }}
          </div>


          <!-- content -->
          <div class="col-lg-9 split-content">


            <!-- text -->
            <section class="pb-5 border-bottom">
              <h1 class="mb-1">Smoothscroll</h1>
            </section>


            <!-- default -->
            <section>
              <h3 class="fs-4">Example</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white">
                  <a data-scroll href="#footer" class="btn btn-primary rounded-pill">Scroll to Footer</a>
                </div>
                <div class="card-footer">
                  <script type="text/plain" class="language-html">
                    <a data-scroll href="#footer" class="btn btn-primary rounded-pill">Scroll to Footer</a>
                  </script>
                </div>
              </div>
            </section>


          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- table of contents - mobile-->
  {{> components/toc-mobile }}


  <!-- javascript -->
  {{> partials/scripts}}
</body>

</html>