<!doctype html>
<html lang="en">

<head>
  {{> partials/head title="Forms - Components"}}
</head>

<body class="bg-light">


  <!-- navbar -->
  {{> navbar/navbar
  logo="/assets/images/logo/logo-dark.svg"
  class="navbar-sticky navbar-light bg-light border-bottom"
  active="components"
  button="true"
  }}


  <div class="offcanvas-wrap">
    <section class="split">
      <div class="container">
        <div class="row justify-content-between">



          <div class="col-lg-3 split-sidebar">
            <!-- table of contents-->
            {{> components/toc
            section="components"
            active="forms"
            }}
          </div>


          <!-- content -->
          <div class="col-lg-9 split-content">


            <!-- text -->
            <section class="pb-5 border-bottom">
              <h1 class="mb-1">Forms</h1>
              <p class="lead text-secondary">Examples and usage guidelines for form control styles, layout options, and
                custom components for creating a wide variety of forms.</p>
              <a href="https://getbootstrap.com/docs/5.0/forms/overview/" class="underline action">Bootstrap
                documentation <i class="bi bi-arrow-up-right"></i></a>
            </section>


            <!-- example -->
            <section>
              <h3 class="fs-4">Example</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white">
                  <form>
                    <div class="mb-3">
                      <label for="exampleInputEmail1" class="form-label">Email address</label>
                      <input type="email" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
                      <div id="emailHelp" class="form-text">We'll never share your email with anyone else.</div>
                    </div>
                    <div class="mb-3">
                      <label for="exampleInputPassword1" class="form-label">Password</label>
                      <input type="password" class="form-control" id="exampleInputPassword1">
                    </div>
                    <div class="mb-3 form-check">
                      <input type="checkbox" class="form-check-input" id="exampleCheck1">
                      <label class="form-check-label" for="exampleCheck1">Check me out</label>
                    </div>
                    <button type="submit" class="btn btn-primary">Submit</button>
                  </form>
                </div>
              </div>
            </section>

            <!-- sizes -->
            <section>
              <h3 class="fs-4">Sizes</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="mb-3">
                        <label for="exampleInputSM" class="form-label">Small</label>
                        <input type="text" class="form-control form-control-sm" id="exampleInputSM">
                      </div>
                      <div class="mb-3">
                        <label for="exampleInput" class="form-label">Regular</label>
                        <input type="text" class="form-control" id="exampleInput">
                      </div>
                      <div>
                        <label for="exampleInputLG" class="form-label">Large</label>
                        <input type="text" class="form-control form-control-lg" id="exampleInputLG">
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="mb-3">
                        <label class="form-label">Small</label>
                        <select class="form-select form-select-sm" aria-label="Default select example">
                          <option selected>Open this select menu</option>
                          <option value="1">One</option>
                          <option value="2">Two</option>
                          <option value="3">Three</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label class="form-label">Regular</label>
                        <select class="form-select" aria-label="Default select example">
                          <option selected>Open this select menu</option>
                          <option value="1">One</option>
                          <option value="2">Two</option>
                          <option value="3">Three</option>
                        </select>
                      </div>
                      <div>
                        <label class="form-label">Large</label>
                        <select class="form-select form-select-lg" aria-label="Default select example">
                          <option selected>Open this select menu</option>
                          <option value="1">One</option>
                          <option value="2">Two</option>
                          <option value="3">Three</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>


            <!-- grouped -->
            <section>
              <h3 class="fs-4">Grouped Inputs</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white">
                  <div class="grouped-inputs p-1 rounded-pill border">
                    <div class="row g-0">
                      <div class="col-md-9">
                        <input type="text" class="form-control form-control-lg px-4"
                          aria-label="Text input with dropdown button" placeholder="What are you looking for ?">
                      </div>
                      <div class="col-md-3 d-grid">
                        <a href="" class="btn btn-primary btn-lg rounded-pill">Search</a>
                      </div>
                    </div>
                  </div>

                  <div class="grouped-inputs p-1 rounded-pill border mt-3">
                    <div class="row g-0">
                      <div class="col">
                        <input type="text" class="form-control form-control-lg px-4 text-primary"
                          aria-label="Text input with dropdown button" placeholder="What are you looking for ?">
                      </div>
                      <div class="col-auto d-grid">
                        <a href="" class="btn btn-primary btn-lg btn-icon rounded-circle"><i
                            class="bi bi-search"></i></a>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card-footer">
                  <script type="text/plain" class="language-html">
                    <div class="grouped-inputs p-1 rounded-pill border">
                      <div class="row g-0">
                        <div class="col-md-9">
                          <input type="text" class="form-control form-control-lg px-4"
                            aria-label="Text input with dropdown button" placeholder="What are you looking for ?">
                        </div>
                        <div class="col-md-3 d-grid">
                          <a href="" class="btn btn-primary btn-lg rounded-pill">Search</a>
                        </div>
                      </div>
                    </div>

                    <div class="grouped-inputs p-1 rounded-pill border">
                      <div class="row g-0">
                        <div class="col">
                          <input type="text" class="form-control form-control-lg px-4 text-primary"
                            aria-label="Text input with dropdown button" placeholder="What are you looking for ?">
                        </div>
                        <div class="col-auto d-grid">
                          <a href="" class="btn btn-primary btn-lg btn-icon rounded-circle"><i
                              class="bi bi-search"></i></a>
                        </div>
                      </div>
                    </div>
                  </script>
                </div>
              </div>
            </section>


            <!-- checkboxes -->
            <section>
              <h3 class="fs-4">Checkboxes, Radio and Switches</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white">
                  <div class="row g-3">
                    <div class="col-md-4">
                      <div class="form-check">
                        <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1">
                        <label class="form-check-label" for="flexRadioDefault1">
                          Radio
                        </label>
                      </div>
                      <div class="form-check mt-3">
                        <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault2"
                          checked>
                        <label class="form-check-label" for="flexRadioDefault2">
                          Checked radio
                        </label>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                        <label class="form-check-label" for="flexCheckDefault">
                          Checkbox
                        </label>
                      </div>
                      <div class="form-check mt-3">
                        <input class="form-check-input" type="checkbox" value="" id="flexCheckChecked" checked>
                        <label class="form-check-label" for="flexCheckChecked">
                          Checked checkbox
                        </label>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="flexSwitchCheckDefault">
                        <label class="form-check-label" for="flexSwitchCheckDefault">Switch</label>
                      </div>
                      <div class="form-check form-switch mt-3">
                        <input class="form-check-input" type="checkbox" id="flexSwitchCheckChecked" checked>
                        <label class="form-check-label" for="flexSwitchCheckChecked">Checked Switch</label>
                      </div>
                    </div>
                  </div>

                </div>
                <div class="card-footer">
                  <script type="text/plain" class="language-html">
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1">
                      <label class="form-check-label" for="flexRadioDefault1">
                        Radio
                      </label>
                    </div>
                    <div class="form-check mt-3">
                      <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault2"
                        checked>
                      <label class="form-check-label" for="flexRadioDefault2">
                        Checked radio
                      </label>
                    </div>

                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                      <label class="form-check-label" for="flexCheckDefault">
                        Checkbox
                      </label>
                    </div>
                    <div class="form-check mt-3">
                      <input class="form-check-input" type="checkbox" value="" id="flexCheckChecked" checked>
                      <label class="form-check-label" for="flexCheckChecked">
                        Checked checkbox
                      </label>
                    </div>

                    <div class="form-check form-switch">
                      <input class="form-check-input" type="checkbox" id="flexSwitchCheckDefault">
                      <label class="form-check-label" for="flexSwitchCheckDefault">Switch</label>
                    </div>
                    <div class="form-check form-switch mt-3">
                      <input class="form-check-input" type="checkbox" id="flexSwitchCheckChecked" checked>
                      <label class="form-check-label" for="flexSwitchCheckChecked">Checked Switch</label>
                    </div>
                  </script>
                </div>
              </div>
            </section>


            <!-- range slider -->
            <section>
              <h3 class="fs-4">Range Slider</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white">
                  <div class="range-slider" data-range='{"step": 1,"connect": true, "start" : [20,80], "range" : {"min": 0, "max" :
                  100}}'></div>
                  <div class="range-slider-selection">Price: <span class="range-slider-value" id="range-min"></span>
                    &mdash; <span class="range-slider-value" id="range-max"></span></div>
                </div>
                <div class="card-footer">
                  <script type="text/plain" class="language-html">
                    <div class="range-slider" data-range='{"step": 1,"connect": true, "start" : [20,80], "range" : {"min": 0, "max" :
                    100}}'></div>
                    <div class="range-slider-selection">Price: <span class="range-slider-value" id="range-min"></span>
                      &mdash; <span class="range-slider-value" id="range-max"></span></div></script>
                </div>
              </div>
            </section>


          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- table of contents - mobile-->
  {{> components/toc-mobile }}


  <!-- javascript -->
  {{> partials/scripts}}
</body>

</html>