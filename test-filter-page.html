<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Property Filter Test</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .filter-container { margin-bottom: 20px; }
        .property-card { border: 1px solid #ccc; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .status-available { background-color: #d4edda; }
        .status-sold { background-color: #f8d7da; }
        select { padding: 8px; margin: 0 10px; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div id="app">
        <h1>Property Filter Test</h1>
        
        <div class="debug">
            <h3>Debug Info:</h3>
            <p>Total Properties: {{ properties.length }}</p>
            <p>Filtered Properties: {{ filteredProperties.length }}</p>
            <p>Current Filter: {{ filters.propertyStatus }}</p>
        </div>
        
        <div class="filter-container">
            <label>Property Status Filter:</label>
            <select v-model="filters.propertyStatus" @change="applyFilters">
                <option value="available">Available Only</option>
                <option value="sold">Sold Only</option>
                <option value="all">Show All</option>
            </select>
            <button @click="resetFilter">Reset to Available</button>
        </div>
        
        <div class="properties-container">
            <h3>Showing {{ filteredProperties.length }} Properties:</h3>
            <div v-for="property in filteredProperties" :key="property.id" 
                 :class="['property-card', 'status-' + property.status]">
                <h4>{{ property.title }}</h4>
                <p><strong>Status:</strong> {{ property.status }}</p>
                <p><strong>Price:</strong> ${{ property.price?.toLocaleString() }}</p>
                <p><strong>Address:</strong> {{ property.address }}</p>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    properties: [
                        { id: 1, title: "Beautiful 4-bedroom, 2-bath, newly renovated home.", status: "sold", price: 270000, address: "123 Main St" },
                        { id: 2, title: "This 1094 square foot single family home has 3 bedrooms and 2.0 bathrooms.", status: "sold", price: 270000, address: "11564 Rambleridge Rd" },
                        { id: 3, title: "Single Family Residence", status: "sold", price: 250000, address: "456 Oak Ave" },
                        { id: 4, title: "Single Family Residence", status: "available", price: 210000, address: "789 Pine St" },
                        { id: 5, title: "Single Family Residence", status: "sold", price: 267500, address: "321 Elm Dr" },
                        { id: 6, title: "Single Family Residence", status: "sold", price: 178000, address: "654 Maple Ln" }
                    ],
                    filteredProperties: [],
                    filters: {
                        propertyStatus: 'available'
                    }
                }
            },
            mounted() {
                this.applyFilters();
            },
            methods: {
                applyFilters() {
                    console.log('Applying filters with status:', this.filters.propertyStatus);
                    this.filteredProperties = this.applyBaseFilters();
                    console.log('Filtered result count:', this.filteredProperties.length);
                },
                applyBaseFilters() {
                    return this.properties.filter(property => {
                        // Status filter - three options: available, sold, or all
                        if (this.filters.propertyStatus === 'available' && property.status !== 'available') {
                            return false;
                        }
                        if (this.filters.propertyStatus === 'sold' && property.status !== 'sold') {
                            return false;
                        }
                        // If propertyStatus is 'all', show all properties regardless of status
                        return true;
                    });
                },
                resetFilter() {
                    this.filters.propertyStatus = 'available';
                    this.applyFilters();
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
