<!doctype html>
<html lang="en">

<head>
  {{> partials/head title="Index"}}
</head>

<body>


  {{> navbar/navbar
  logo="/assets/images/logo/logo-light.svg"
  class="navbar-sticky navbar-dark"
  button="btn-outline-white"
  }}


  <div class="offcanvas-wrap">


    <section class="bg-black inverted overflow-hidden">
      <div class="container py-20 d-flex flex-column min-vh-100 foreground">
        <div class="row my-auto justify-content-center">
          <div class="col-lg-6 text-center" data-center-center="transform: translateY(0%);"
            data-top-bottom="transform: translateY(10%);">
            <span class="badge bg-opaque-red rounded-pill text-red mb-4">Cube 2.0.0</span>
            <h1 class="display-1 text-shadow">Building Websites Just Got Easier</h1>
          </div>
        </div>
      </div>
      <figure class="background background-gradient-vertical" data-aos="zoom-out" data-aos-delay="400">
        <img class="w-40 shadow position-absolute top-10 end-70" src="./assets/images/blocks/block-1.jpg" alt=""
          data-center-top="transform: translateY(0%);" data-top-bottom="transform: translateY(-80%);">

        <img class="w-40 shadow position-absolute top-80 start-20" src="./assets/images/blocks/block-2.jpg" alt=""
          data-bottom-top="transform: translateY(0%);" data-top-bottom="transform: translateY(-50%);">

        <img class="w-40 shadow position-absolute top-0 start-60" src="./assets/images/blocks/block-3.jpg" alt=""
          data-bottom-top="transform: translateY(0%);" data-top-bottom="transform: translateY(-50%);">

        <img class="w-40 shadow position-absolute top-60 start-80" src="./assets/images/blocks/block-4.jpg" alt=""
          data-bottom-top="transform: translateY(0%);" data-top-bottom="transform: translateY(-120%);">

        <img class="w-40 shadow position-absolute top-100 start-50" src="./assets/images/blocks/block-5.jpg" alt=""
          data-bottom-top="transform: translateY(0%);" data-top-bottom="transform: translateY(-60%);">

        <img class="w-40 shadow position-absolute top-60 end-90" src="./assets/images/blocks/block-6.jpg" alt=""
          data-bottom-top="transform: translateY(0%);" data-top-bottom="transform: translateY(-80%);">
      </figure>
      <span class="scroll-down"></span>
    </section>



    <!-- demo -->
    <section id="demo" class="py-15 py-xl-20 bg-light">
      <div class="container">
        <div class="row justify-content-center mb-10">
          <div class="col-lg-10 col-xl-8 text-center">
            <h2 class="display-2 fw-light"><span class="fw-bold">50+ prebuilt pages</span> & unlimited variations</h2>
            <button class="btn btn-filter rounded-pill current" data-filter="*" data-target="#grid1">all</button>
            <button class="btn btn-filter rounded-pill" data-filter=".landing" data-target="#grid1">landings</button>
            <button class="btn btn-filter rounded-pill" data-filter=".page" data-target="#grid1">pages</button>
            <button class="btn btn-filter rounded-pill" data-filter=".account" data-target="#grid1">account</button>
            <button class="btn btn-filter rounded-pill" data-filter=".shop" data-target="#grid1">shop</button>
          </div>
        </div>

        <div class="row g-3 g-xl-5" id="grid1" data-aos="fade-up" data-isotope>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="startup.html"
            title="Startup"
            image="./assets/images/pages/startup.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="saas.html"
            title="Saas"
            image="./assets/images/pages/saas.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="coworking.html"
            title="Coworking"
            image="./assets/images/pages/coworking.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="job-board.html"
            title="Job Board"
            image="./assets/images/pages/job-board.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="agency.html"
            title="Agency"
            image="./assets/images/pages/agency.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="blog.html"
            title="Blog"
            image="./assets/images/pages/blog.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="product.html"
            title="Product"
            image="./assets/images/pages/product.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="app.html"
            title="App"
            image="./assets/images/pages/app.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="shop.html"
            title="Shop"
            image="./assets/images/pages/shop.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="event.html"
            title="Event"
            image="./assets/images/pages/event.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="course.html"
            title="Course"
            image="./assets/images/pages/course.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="service.html"
            title="Service"
            image="./assets/images/pages/service.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="software.html"
            title="Software"
            image="./assets/images/pages/software.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 landing">
            {{> components/card-portfolio
            link="documentation.html"
            title="Documentation"
            image="./assets/images/pages/documentation.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="about.html"
            title="About"
            image="./assets/images/pages/about.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="pricing.html"
            title="Pricing"
            image="./assets/images/pages/pricing.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="faq.html"
            title="FAQ"
            image="./assets/images/pages/faq.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="terms.html"
            title="Terms"
            image="./assets/images/pages/terms.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="services.html"
            title="Services"
            image="./assets/images/pages/services.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="job-listing.html"
            title="Job Listing"
            image="./assets/images/pages/job-listing.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="job-post.html"
            title="Job Post"
            image="./assets/images/pages/job-post.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="portfolio-grid.html"
            title="Portfolio Grid"
            image="./assets/images/pages/portfolio-grid.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="portfolio-list.html"
            title="Portfolio List"
            image="./assets/images/pages/portfolio-list.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="case-study.html"
            title="Case Study"
            image="./assets/images/pages/case-study.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="blog-listing.html"
            title="Blog Listing"
            image="./assets/images/pages/blog-listing.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="blog-post.html"
            title="Blog Post"
            image="./assets/images/pages/blog-post.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="contact.html"
            title="Contact Classic"
            image="./assets/images/pages/contact.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="contact-location.html"
            title="Contact Location"
            image="./assets/images/pages/contact-location.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="404.html"
            title="404"
            image="./assets/images/pages/404.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 page">
            {{> components/card-portfolio
            link="coming-soon.html"
            title="Coming Soon"
            image="./assets/images/pages/coming-soon.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 account">
            {{> components/card-portfolio
            link="account.html"
            title="Dashboard"
            image="./assets/images/pages/account.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 account">
            {{> components/card-portfolio
            link="account-settings.html"
            title="Settings"
            image="./assets/images/pages/account-settings.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 account">
            {{> components/card-portfolio
            link="account-orders.html"
            title="Orders"
            image="./assets/images/pages/account-orders.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 account">
            {{> components/card-portfolio
            link="account-billing.html"
            title="Billing"
            image="./assets/images/pages/account-billing.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 account">
            {{> components/card-portfolio
            link="sign-in.html"
            title="Sign In"
            image="./assets/images/pages/sign-in.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 account">
            {{> components/card-portfolio
            link="register.html"
            title="Register"
            image="./assets/images/pages/register.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 account">
            {{> components/card-portfolio
            link="forgot-password.html"
            title="Forgot Password"
            image="./assets/images/pages/forgot-password.jpg"
            }}
          </div>


          <div class="col-6 col-lg-4 shop">
            {{> components/card-portfolio
            link="shop-product.html"
            title="Product Page"
            image="./assets/images/pages/shop-product.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 shop">
            {{> components/card-portfolio
            link="shop-listing.html"
            title="Listing"
            image="./assets/images/pages/shop-listing.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 shop">
            {{> components/card-portfolio
            link="shop-listing-sidebar.html"
            title="Listing Sidebar"
            image="./assets/images/pages/shop-listing-sidebar.jpg"
            }}
          </div>

          <div class="col-6 col-lg-4 shop">
            {{> components/card-portfolio
            link="shop-cart.html"
            title="Cart"
            image="./assets/images/pages/shop-cart.jpg"
            }}
          </div>


        </div>

      </div>
    </section>



    <!-- features -->
    <section class="py-15 py-xl-20">
      <div class="container">
        <div class="row mb-8">
          <div class="col-lg-10">
            <h2>Cube is packed with <span class="d-lg-block">features you' ll love.</span>
            </h2>
          </div>
        </div>
        <div class="row g-1 g-lg-3">
          <div class="col-auto">
            <div class="d-flex align-items-center border rounded-pill pe-4">
              <img src="./assets/images/logo/bootstrap.svg" class="me-3" alt="Bootstrap">
              Built on Bootstrap 5
            </div>
          </div>
          <div class="col-auto">
            <div class="d-flex align-items-center border rounded-pill pe-4">
              <img src="./assets/images/logo/js.svg" class="me-3" alt="Javascript">
              Vanilla Javascript
            </div>
          </div>
          <div class="col-auto">
            <div class="d-flex align-items-center border rounded-pill pe-4">
              <img src="./assets/images/logo/webpack.svg" class="me-3" alt="Webpack">
              Webpack Setup
            </div>
          </div>
          <div class="col-auto">
            <div class="d-flex align-items-center border rounded-pill pe-4">
              <img src="./assets/images/logo/w3c.svg" class="me-3" alt="W3C">
              W3C Valid HTML Code
            </div>
          </div>
          <div class="col-auto">
            <div class="d-flex align-items-center border rounded-pill pe-4">
              <img src="./assets/images/logo/sass.svg" class="me-3" alt="Saas">
              Easy Customisable with SASS
            </div>
          </div>
          <div class="col-auto">
            <div class="d-flex align-items-center border rounded-pill pe-4">
              <img src="./assets/images/logo/seo.svg" class="me-3" alt="Seo"> SEO Friendly Code
            </div>
          </div>
          <div class="col-auto">
            <div class="d-flex align-items-center border rounded-pill pe-4">
              <img src="./assets/images/logo/documentation.svg" class="me-3" alt="Documentation"> Detailed Documentation
            </div>
          </div>
          <div class="col-auto">
            <div class="d-flex align-items-center border rounded-pill pe-4">
              <img src="./assets/images/logo/responsive.svg" class="me-3" alt="Documentation"> Fully Responsive
            </div>
          </div>
        </div>
      </div>
    </section>



    <!-- offer -->
    <section class="py-15 py-xl-20 overflow-hidden border-top">
      <div class="container">
        <div class="row justify-content-between align-items-center gutter-3 gutter-lg-5">
          <div class="col-lg-5">
            <div class="row mb-6">
              <div class="col">
                <h2><a href="https://themes.getbootstrap.com/product/cube-multipurpose-template-ui-kit/"
                    class="text-primary underline">Buy now</a> and get one new
                  demo every month.</h2>
                <div class="mt-5">
                  <span class="eyebrow mb-2 text-muted">Next Demo will be realeased in:</span>
                  <div class="countdown" data-countdown="Jul 25, 2021 21:00:00"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-6">
            <div class="position-relative">
              <img class="img-fluid shadow-lg level-1" src="./assets/images/pages/shop.jpg" alt="Image"
                data-aos="fade-up" data-aos-delay="800">
              <img class="img-fluid shadow-lg position-absolute level-2 start-10 top-20"
                src="./assets/images/pages/about.jpg" alt="Image" data-aos="fade-up" data-aos-delay="400">
              <img class="img-fluid shadow-lg position-absolute level-3 start-20 bottom-20"
                src="./assets/images/pages/services.jpg" alt="Image" data-aos="fade-up">
            </div>
          </div>
        </div>
      </div>

    </section>




    <section class="py-15 py-xl-20 bg-black inverted">
      <div class="container">
        <div class="row">
          <div class="col-lg-8">
            <div class="accordion accordion-minimal accordion-highlight" id="accordion-1">
              <div class="accordion-item">
                <h2 class="accordion-header" id="heading-1-1">
                  <button class="accordion-button fs-4 collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapse-1-1" aria-expanded="false" aria-controls="collapse-1-1">
                    Can I use only static HTML files ?
                  </button>
                </h2>
                <div id="collapse-1-1" class="accordion-collapse collapse" aria-labelledby="heading-1-1"
                  data-bs-parent="#accordion-1">
                  <div class="accordion-body">
                    <p class="text-secondary">
                      <span class="fw-bold text-white">Yes.</span> The gulp workflow can be bypassed all together if you
                      prefer to simply edit the static HTML and CSS files.
                    </p>
                  </div>
                </div>
              </div>
              <div class="accordion-item">
                <h2 class="accordion-header" id="heading-1-2">
                  <button class="accordion-button fs-4 collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapse-1-2" aria-expanded="false" aria-controls="collapse-1-2">
                    Are images and videos included in the package?
                  </button>
                </h2>
                <div id="collapse-1-2" class="accordion-collapse collapse" aria-labelledby="heading-1-2"
                  data-bs-parent="#accordion-1">
                  <div class="accordion-body">
                    <p class="text-secondary">
                      <span class="fw-bold text-white">Yes.</span> All images you see in the demo are included in the
                      download package.
                    </p>
                  </div>
                </div>
              </div>
              <div class="accordion-item">
                <h2 class="accordion-header" id="heading-1-3">
                  <button class="accordion-button fs-4 collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapse-1-3" aria-expanded="false" aria-controls="collapse-1-3">
                    How can I request suport?
                  </button>
                </h2>
                <div id="collapse-1-3" class="accordion-collapse collapse" aria-labelledby="heading-1-3"
                  data-bs-parent="#accordion-1">
                  <div class="accordion-body">
                    <p class="text-secondary">Our support team is standing by to offer quick and professional one-on-one
                      support to all of our customers. </p>
                    <a href="mailto:<EMAIL>"
                      class="btn btn-sm btn-with-icon btn-primary rounded-pill">Get in touch <i
                        class="icon-send"></i></a>
                  </div>
                </div>
              </div>
              <div class="accordion-item">
                <h2 class="accordion-header" id="heading-1-4">
                  <button class="accordion-button fs-4 collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapse-1-4" aria-expanded="false" aria-controls="collapse-1-4">
                    Do I get free updates?
                  </button>
                </h2>
                <div id="collapse-1-4" class="accordion-collapse collapse" aria-labelledby="heading-1-4"
                  data-bs-parent="#accordion-1">
                  <div class="accordion-body">
                    <p class="text-secondary">
                      <span class="fw-bold text-white">Yes.</span> All of our customers take advantage of lifetime
                      updates.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-4">
            <img class="img-fluid" src="./assets/images/svg/faq.svg" alt="Figure">
          </div>
        </div>
      </div>
    </section>


    <!-- footer -->
    {{> footer/footer-3
    logo="/assets/images/logo/logo-light.svg"
    class="py-10 py-xl-15 bg-black inverted border-top"
    }}
  </div>

  <!-- javascript -->
  {{> partials/scripts}}
</body>

</html>