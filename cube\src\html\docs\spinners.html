<!doctype html>
<html lang="en">

<head>
  {{> partials/head title="Spinners - Documentation"}}
</head>

<body class="bg-light">


  <!-- navbar -->
  {{> navbar/navbar
  logo="/assets/images/logo/logo-dark.svg"
  class="navbar-sticky navbar-light bg-light border-bottom"
  active="components"
  button="true"
  }}


  <div class="offcanvas-wrap">
    <section class="split">
      <div class="container">
        <div class="row justify-content-between">



          <div class="col-lg-3 split-sidebar">
            <!-- table of contents-->
            {{> components/toc
            section="components"
            active="spinners"
            }}
          </div>


          <!-- content -->
          <div class="col-lg-9 split-content">


            <!-- text -->
            <section class="pb-5 border-bottom">
              <h1 class="mb-1">Spinners</h1>
              <p class="lead text-secondary">Indicate the loading state of a component or page with Bootstrap spinners,
                built entirely with HTML, CSS, and no JavaScript.</p>
              <a href="https://getbootstrap.com/docs/5.0/components/spinners/" class="underline action">Bootstrap
                Documentation <i class="bi bi-arrow-up-right"></i></a>
            </section>


            <!-- border spinner -->
            <section>
              <h3 class="fs-4">Border Spinner</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white">
                  <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                </div>
                <div class="card-footer">
                  <script type="text/plain" class="language-html">
                    <div class="spinner-border" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                  </script>
                </div>
              </div>
            </section>


            <!-- colors -->
            <section>
              <h3 class="fs-4">Colors</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <div class="spinner-border text-secondary" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <div class="spinner-border text-success" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <div class="spinner-border text-danger" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <div class="spinner-border text-warning" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <div class="spinner-border text-info" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <div class="spinner-border text-light" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <div class="spinner-border text-dark" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                </div>
                <div class="card-footer">
                  <script type="text/plain" class="language-html">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="spinner-border text-secondary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="spinner-border text-success" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="spinner-border text-danger" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="spinner-border text-warning" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="spinner-border text-info" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="spinner-border text-light" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="spinner-border text-dark" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                  </script>
                </div>
              </div>
            </section>


            <!-- growing spinner -->
            <section>
              <h3 class="fs-4">Growing Spinner</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white">
                  <div class="spinner-grow text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                </div>
                <div class="card-footer">
                  <script type="text/plain" class="language-html">
                    <div class="spinner-grow text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                  </script>
                </div>
              </div>
            </section>

          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- table of contents - mobile-->
  {{> components/toc-mobile }}


  <!-- javascript -->
  {{> partials/scripts}}
</body>

</html>