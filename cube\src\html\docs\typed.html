<!doctype html>
<html lang="en">

<head>
  {{> partials/head title="Steps - Documentation"}}
</head>

<body class="bg-light">


  <!-- navbar -->
  {{> navbar/navbar
  logo="/assets/images/logo/logo-dark.svg"
  class="navbar-sticky navbar-light bg-light border-bottom"
  active="components"
  button="btn-primary"
  }}


  <div class="offcanvas-wrap">
    <section class="split">
      <div class="container">
        <div class="row justify-content-between">



          <div class="col-lg-3 split-sidebar">
            <!-- table of contents-->
            {{> components/toc
            section="components"
            active="typed"
            }}
          </div>


          <!-- content -->
          <div class="col-lg-9 split-content">


            <!-- text -->
            <section class="pb-5 border-bottom">
              <h1 class="mb-1">Typed</h1>
              <p class="text-secondary lead">Typed.js is a library that types. Enter in any string, and watch it type at
                the speed you've set,
                backspace what it's typed, and begin a new sentence for however many strings you've set.</p>
              <a href="" class="underline action" target="_blank">Typed Documentation <i
                  class="bi bi-arrow-up-right"></i></a>
            </section>


            <!-- example -->
            <section>
              <h3 class="fs-4">Example</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white overflow-hidden">
                  <h2>Typed is <span data-typed='{"strings": ["awesome.", "creative.", "usefull."]}'></span></h2>
                </div>
                <div class="card-footer">
                  <script type="text/plain" class="language-html">
                    <h2>Typed is <span data-typed='{"strings": ["awesome.", "creative.", "usefull."]}'></span></h2>
                  </script>
                </div>
              </div>
            </section>


          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- table of contents - mobile-->
  {{> components/toc-mobile }}


  <!-- javascript -->
  {{> partials/scripts}}
</body>

</html>