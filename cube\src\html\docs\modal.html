<!doctype html>
<html lang="en">

<head>
  {{> partials/head title="Modal - Documentation"}}
</head>

<body class="bg-light">


  <!-- navbar -->
  {{> navbar/navbar
  logo="/assets/images/logo/logo-dark.svg"
  class="navbar-sticky navbar-light bg-light border-bottom"
  active="components"
  button="true"
  }}


  <div class="offcanvas-wrap">
    <section class="split">
      <div class="container">
        <div class="row justify-content-between">



          <div class="col-lg-3 split-sidebar">
            <!-- table of contents-->
            {{> components/toc
            section="components"
            active="modal"
            }}
          </div>


          <!-- content -->
          <div class="col-lg-9 split-content">


            <!-- text -->
            <section class="pb-5 border-bottom">
              <h1 class="mb-1">Modal</h1>
              <p class="lead text-secondary">Use Bootstrap’s JavaScript modal plugin to add dialogs to your site for
                lightboxes, user notifications, or completely custom content.</p>
              <a href="https://getbootstrap.com/docs/5.0/components/modal/" class="underline action">Bootstrap
                Documentation <i class="bi bi-arrow-up-right"></i></a>
            </section>


            <!-- example -->
            <section>
              <h3 class="fs-4">Example</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white">
                  <button type="button" class="btn btn-primary rounded-pill" data-bs-toggle="modal"
                    data-bs-target="#modal-1">
                    Launch demo modal
                  </button>
                </div>
                <div class="card-footer">
                  <script type="text/plain" class="language-html">
                    <button type="button" class="btn btn-primary rounded-pill" data-bs-toggle="modal"
                    data-bs-target="#modal-1">
                    Launch demo modal
                  </button>
                  
                    <div class="modal fade" id="modal-1" tabindex="-1" aria-labelledby="modalLabel" aria-hidden="true">
                      <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                          <button type="button" class="bi bi-x modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
                          <div class="modal-body p-8 text-center">
                            <h3 id="modalLabel">Save on Premium Membership</h3>
                            <p class="text-secondary">Lorem ipsum dolor sit amet consectetur adipisicing elit. Aperiam saepe tempore illo
                              dolorem, maiores, sit
                              vitae ducimus officiis reprehenderit, laudantium recusandae. Laborum iste aperiam harum.</p>
                            <div class="d-grid gap-1 w-100 mt-3">
                              <button type="button" class="btn btn-primary rounded-pill">Save changes</button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </script>
                </div>
              </div>
            </section>


            <!-- example -->
            <section>
              <h3 class="fs-4">With Image</h3>

              <div class="card bg-opaque-white">
                <div class="card-body bg-white">
                  <button type="button" class="btn btn-primary rounded-pill" data-bs-toggle="modal"
                    data-bs-target="#modal-2">
                    Launch demo modal
                  </button>
                </div>
                <div class="card-footer">
                  <script type="text/plain" class="language-html">
                    <button type="button" class="btn btn-primary rounded-pill" data-bs-toggle="modal"
                    data-bs-target="#modal-2">
                    Launch demo modal
                  </button>
                  
                  <div class="modal fade" id="modal-2" tabindex="-1" aria-labelledby="modalLabel-2" aria-hidden="true">
                    <div class="modal-dialog modal-xl modal-dialog-centered">
                      <div class="modal-content">
                        <button type="button" class="bi bi-x modal-close text-white" data-bs-dismiss="modal"
                          aria-label="Close"></button>
                        <div class="row g-0">
                          <div class="col">
                            <div class="modal-body p-8">
                              <h3 id="modalLabel-2">Save on Premium Membership</h3>
                              <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Aperiam saepe tempore illo dolorem, maiores,
                                sit
                                vitae ducimus officiis reprehenderit, laudantium recusandae. Laborum iste aperiam harum. Enim eligendi
                                molestiae natus saepe.</p>
                              <div class="d-grid gap-1 w-100 mt-3">
                                <button type="button" class="btn btn-primary rounded-pill">Save changes</button>
                              </div>
                            </div>
                          </div>
                          <div class="col position-relative">
                            <figure class="background background-overlay"
                              style="background-image: url('../assets/images/image-1.jpg')"></figure>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  </script>
                </div>
              </div>
            </section>


          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- table of contents - mobile-->
  {{> components/toc-mobile }}




  <!-- Modal -->
  <div class="modal fade" id="modal-1" tabindex="-1" aria-labelledby="modalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <button type="button" class="bi bi-x modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        <div class="modal-body p-8 text-center">
          <h3 id="modalLabel">Save on Premium Membership</h3>
          <p class="text-secondary">Lorem ipsum dolor sit amet consectetur adipisicing elit. Aperiam saepe tempore illo
            dolorem, maiores, sit
            vitae ducimus officiis reprehenderit, laudantium recusandae. Laborum iste aperiam harum.</p>
          <div class="d-grid gap-1 w-100 mt-3">
            <button type="button" class="btn btn-primary rounded-pill">Save changes</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal -->
  <div class="modal fade" id="modal-2" tabindex="-1" aria-labelledby="modalLabel-2" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content">
        <button type="button" class="bi bi-x modal-close text-white" data-bs-dismiss="modal"
          aria-label="Close"></button>
        <div class="row g-0">
          <div class="col">
            <div class="modal-body p-8">
              <h3 id="modalLabel-2">Save on Premium Membership</h3>
              <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Aperiam saepe tempore illo dolorem, maiores,
                sit
                vitae ducimus officiis reprehenderit, laudantium recusandae. Laborum iste aperiam harum. Enim eligendi
                molestiae natus saepe.</p>
              <div class="d-grid gap-1 w-100 mt-3">
                <button type="button" class="btn btn-primary rounded-pill">Save changes</button>
              </div>
            </div>
          </div>
          <div class="col position-relative">
            <figure class="background background-overlay" style="background-image: url('../assets/images/small-1.jpg')">
            </figure>
          </div>
        </div>
      </div>
    </div>
  </div>




  <!-- javascript -->
  {{> partials/scripts}}
</body>

</html>